// sanity/schemas/faq.js
export default {
  name: 'faq',
  title: 'FAQ - Często zadawane pytania',
  type: 'document',
  fields: [
    {
      name: 'question',
      title: 'Pytanie',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'answer',
      title: 'Od<PERSON>wied<PERSON>',
      type: 'array',
      of: [
        {
          type: 'block'
        }
      ],
      validation: Rule => Rule.required()
    },
    {
      name: 'category',
      title: 'Kategoria',
      type: 'string',
      options: {
        list: [
          {title: 'Ogólne', value: 'general'},
          {title: 'Kosz<PERSON> i płatności', value: 'costs'},
          {title: 'Przygotowania do wyjazdu', value: 'preparation'},
          {title: 'Praktyka jogi', value: 'yoga'},
          {title: 'Zakwaterowanie', value: 'accommodation'},
          {title: 'Transport', value: 'transport'},
          {title: 'Zdrowie i bezpieczeństwo', value: 'health'},
          {title: 'Dokumenty i formalności', value: 'documents'}
        ]
      },
      initialValue: 'general'
    },
    {
      name: 'order',
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wyświetlania',
      type: 'number',
      description: '<PERSON><PERSON><PERSON>e liczby będą wyświetlane wcześniej'
    },
    {
      name: 'featured',
      title: 'Wyróżnione pytanie',
      type: 'boolean',
      description: 'Czy pokazywać na stronie głównej?',
      initialValue: false
    },
    {
      name: 'keywords',
      title: 'Słowa kluczowe',
      type: 'array',
      of: [{type: 'string'}],
      description: 'Słowa kluczowe ułatwiające wyszukiwanie'
    },
    {
      name: 'relatedRetreats',
      title: 'Powiązane retreaty',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'retreat'}]
        }
      ],
      description: 'Retreaty, których dotyczy to pytanie'
    }
  ],
  preview: {
    select: {
      question: 'question',
      category: 'category',
      featured: 'featured',
      order: 'order'
    },
    prepare(selection) {
      const {question, category, featured, order} = selection
      const categoryLabels = {
        general: '📋 Ogólne',
        costs: '💰 Koszty',
        preparation: '🎒 Przygotowania',
        yoga: '🧘‍♀️ Joga',
        accommodation: '🏨 Zakwaterowanie',
        transport: '✈️ Transport',
        health: '🏥 Zdrowie',
        documents: '📄 Dokumenty'
      }
      
      const status = []
      if (featured) status.push('⭐ Wyróżnione')
      if (order) status.push(`#${order}`)
      
      return {
        title: question,
        subtitle: `${categoryLabels[category] || category}${status.length ? ' | ' + status.join(' ') : ''}`
      }
    }
  },
  orderings: [
    {
      title: 'Kolejność wyświetlania',
      name: 'order',
      by: [
        {field: 'order', direction: 'asc'},
        {field: '_createdAt', direction: 'asc'}
      ]
    },
    {
      title: 'Kategoria',
      name: 'category',
      by: [
        {field: 'category', direction: 'asc'},
        {field: 'order', direction: 'asc'}
      ]
    },
    {
      title: 'Wyróżnione',
      name: 'featured',
      by: [
        {field: 'featured', direction: 'desc'},
        {field: 'order', direction: 'asc'}
      ]
    }
  ]
}
