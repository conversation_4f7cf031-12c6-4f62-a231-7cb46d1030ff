// src/data/programData.js

// Funkcja pomocnicza do generowania slugów
const generateSlug = (day, title, destination = '') => {
  const prefix = destination ? `${destination}-` : '';
  if (!title) return `${prefix}dzien-${day}`;
  return `${prefix}dzien-${day}-${title
    .toLowerCase()
    .replace(/ą/g, 'a').replace(/ć/g, 'c').replace(/ę/g, 'e')
    .replace(/ł/g, 'l').replace(/ń/g, 'n').replace(/ó/g, 'o')
    .replace(/ś/g, 's').replace(/ź/g, 'z').replace(/ż/g, 'z')
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '')
    .replace(/-+/g, '-')}`;
};

// Destinations configuration
export const destinations = {
  bali: {
    id: 'bali',
    name: 'Bali',
    country: 'Indonezja',
    duration: '12 dni',
    description: 'Duchowe serce Azji - joga wśród tarasów ryżowych i świętych świątyń',
    highlights: ['Ubud', 'Gili Air', 'Uluwatu', 'Kelingking'],
    priceRange: '2900-4500 PLN',
    image: '/images/destinations/bali-hero.webp',
    active: true
  },
  srilanka: {
    id: 'srilanka',
    name: 'Sri Lanka',
    country: 'Sri Lanka',
    duration: '10 dni',
    description: 'Perła Oceanu Indyjskiego - ayurveda, joga i starożytne cuda',
    highlights: ['Sigiriya', 'Kandy', 'Ella', 'Galle'],
    priceRange: '2500-3800 PLN',
    image: '/images/destinations/srilanka-hero.webp',
    active: true
  }
};

// Bali Program Data
const rawBaliProgramData = [
  {
    day: 1,
    title: "Dzień przyjazdu",
    image: "/images/programs/program1.webp",
    activities: [
      'Wylot z Polski',
      'Przylot na Bali',
      'Transfer do hotelu w Uluwatu',
      'Zakwaterowanie',
      'Kolacja połączona z integracją (płaci organizator)'
    ]
  },
  {
    day: 2,
    title: "Aklimatyzacja",
    image: "/images/programs/program2.webp",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Przedstawienie programu wycieczki',
      'Aklimatyzacja przy hotelowym basenie',
      'Spacer po mieście',
      'Wieczorna joga'
    ]
  },
  {
    day: 3,
    title: "Kelingking",
    image: "/images/programs/program3.webp",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Wyjazd na plażę Kelingking',
      'Zwiedzanie plaży',
      'Powrót do hotelu',
      'Wieczorna joga'
    ]
  },
  {
    day: 4,
    title: "Padang Padang",
    image: "/images/programs/program4.webp",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Plażowanie na plaży Padang Padang',
      'Wieczorna joga'
    ]
  },
  {
    day: 5,
    title: "Gili Air",
    image: "/images/programs/program5.webp",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Przejazd na wyspę Gili Air',
      'Zakwaterowanie w hotelu',
      'Zwiedzanie wyspy',
      'Wieczorna joga'
    ]
  },
  {
    day: 6,
    title: "Relaks na Gili",
    image: "/images/programs/program6.webp",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Plażowanie',
      'Zwiedzanie wyspy indywidualne',
      'Wieczorna joga'
    ]
  },
  {
    day: 7,
    title: "Plażowanie i Snorkeling",
    image: "/images/programs/program7.webp",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Plażowanie',
      'Snorkeling',
      'Możliwość wycieczek fakultatywnych',
      'Wieczorna joga'
    ]
  },
  {
    day: 8,
    title: "Powrót do Ubud",
    image: "/images/gallery/IMG-20250422-WA0018.webp",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Powrót do Ubud',
      'Wieczorna joga'
    ]
  },
  {
    day: 9,
    title: "Wodospad",
    image: "/images/gallery/IMG-20250422-WA0044.webp",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Wyjazd na wodospad Taman Beji',
      'Wieczorna joga'
    ]
  },
  {
    day: 10,
    title: "Tarasy Ryżowe",
    image: "/images/gallery/IMG-20250422-WA0037.webp",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Zwiedzanie tarasów ryżowych Tegallalang ',
      'Możliwość huśtawki nad tarasami ryzowymi (dodatkowo płatne)',
      'Wieczorna joga'
    ]
  },
  {
    day: 11,
    title: "Ubud",
    image: "/images/gallery/malpi-gaj.webp",
    activities: [
      'Poranna joga',
      'Małpi gaj',
      'Zwiedzanie pałacu Ubud',
      'Wieczorna joga',
      'Kolacja wspólna z grupą (płaci organizator)',
      'Dla chętnych: Wschód słońca na wulkanie (dodatkowo płatne)'
    ]
  },
  {
    day: 12,
    title: "Powrót",
    activities: [
      'Poranna joga',
      'Śniadanie',
      'Wyjazd na lotnisko',
      'Wylot do Polski',
      'Przylot do Polski'
    ]
  }
];

// Sri Lanka Program Data
const rawSriLankaProgramData = [
  {
    day: 1,
    title: "Przylot do Colombo",
    image: "/images/programs/srilanka/colombo-arrival.webp",
    activities: [
      'Wylot z Polski',
      'Przylot do Colombo',
      'Transfer do hotelu w Negombo',
      'Zakwaterowanie i odpoczynek',
      'Kolacja powitalna z grupą'
    ]
  },
  {
    day: 2,
    title: "Sigiriya - Lwia Skała",
    image: "/images/programs/srilanka/sigiriya.webp",
    activities: [
      'Poranna joga i medytacja',
      'Śniadanie',
      'Przejazd do Sigiriya',
      'Zwiedzanie Lwiej Skały',
      'Zakwaterowanie w hotelu',
      'Wieczorna joga pod gwiazdami'
    ]
  },
  {
    day: 3,
    title: "Dambulla i Ayurveda",
    image: "/images/programs/srilanka/dambulla.webp",
    activities: [
      'Poranna joga w ogrodzie',
      'Śniadanie',
      'Zwiedzanie świątyń jaskiniowych Dambulla',
      'Wprowadzenie do ayurvedy',
      'Pierwszy masaż ayurvedyjski',
      'Wieczorna medytacja'
    ]
  },
  {
    day: 4,
    title: "Kandy - Kulturalne Serce",
    image: "/images/programs/srilanka/kandy.webp",
    activities: [
      'Poranna joga nad jeziorem',
      'Śniadanie',
      'Przejazd do Kandy',
      'Zwiedzanie Świątyni Zęba Buddy',
      'Spacer po Królewskim Ogrodzie Botanicznym',
      'Wieczorna joga i pranayama'
    ]
  },
  {
    day: 5,
    title: "Plantacje Herbaty",
    image: "/images/programs/srilanka/tea-plantation.webp",
    activities: [
      'Poranna joga w górach',
      'Śniadanie',
      'Wycieczka na plantację herbaty',
      'Degustacja cejlońskiej herbaty',
      'Warsztat mindful tea ceremony',
      'Wieczorna joga restorative'
    ]
  },
  {
    day: 6,
    title: "Ella - Górski Raj",
    image: "/images/programs/srilanka/ella.webp",
    activities: [
      'Poranna joga na tarasie',
      'Śniadanie',
      'Przejazd malowniczą trasą do Ella',
      'Nine Arch Bridge',
      'Little Adam\'s Peak (opcjonalnie)',
      'Wieczorna joga z widokiem na góry'
    ]
  },
  {
    day: 7,
    title: "Relaks w Ella",
    image: "/images/programs/srilanka/ella-relax.webp",
    activities: [
      'Poranna joga flow',
      'Śniadanie',
      'Dzień relaksu i eksploracji',
      'Ayurvedyjski masaż całego ciała',
      'Medytacja w naturze',
      'Wieczorna joga yin'
    ]
  },
  {
    day: 8,
    title: "Galle - Kolonialne Miasto",
    image: "/images/programs/srilanka/galle.webp",
    activities: [
      'Poranna joga nad oceanem',
      'Śniadanie',
      'Przejazd do Galle',
      'Zwiedzanie holenderskiego fortu',
      'Spacer po starówce',
      'Wieczorna joga na plaży'
    ]
  },
  {
    day: 9,
    title: "Plaże Południowego Wybrzeża",
    image: "/images/programs/srilanka/south-coast.webp",
    activities: [
      'Poranna joga na plaży',
      'Śniadanie',
      'Relaks na pięknych plażach',
      'Obserwacja wielorybów (sezonowo)',
      'Ayurvedyjski masaż stóp',
      'Wieczorna medytacja nad oceanem'
    ]
  },
  {
    day: 10,
    title: "Pożegnanie z Sri Lanką",
    image: "/images/programs/srilanka/departure.webp",
    activities: [
      'Poranna joga pożegnalna',
      'Śniadanie',
      'Transfer na lotnisko w Colombo',
      'Wylot do Polski',
      'Przylot do Polski'
    ]
  }
];

// Helper function to process program data
const processProgramData = (rawData, destination) => {
  return rawData.map(day => {
    const slug = generateSlug(day.day, day.title, destination);
    const destinationName = destinations[destination]?.name || destination;

    // Dodaj opis dla SEO bazując na aktywnościach
    const description = `Dzień ${day.day} wycieczki na ${destinationName}: ${day.title}. W programie: ${day.activities.slice(0, 3).join(', ')} i więcej.`;

    return {
      ...day,
      destination,
      slug,
      seo: {
        title: `Dzień ${day.day}: ${day.title} | Jogowa wycieczka na ${destinationName}`,
        description,
        keywords: `joga ${destinationName.toLowerCase()}, dzień ${day.day}, ${day.title.toLowerCase()}, wycieczka jogowa`
      },
      lastModified: new Date()
    };
  });
};

// Export processed program data
export const baliProgramData = processProgramData(rawBaliProgramData, 'bali');
export const sriLankaProgramData = processProgramData(rawSriLankaProgramData, 'srilanka');

// Combined program data for backward compatibility
export const programData = baliProgramData;

// Function to get program data by destination
export const getProgramByDestination = (destination) => {
  switch (destination) {
    case 'bali':
      return baliProgramData;
    case 'srilanka':
      return sriLankaProgramData;
    default:
      return baliProgramData;
  }
};

// Function to get all destinations
export const getAllDestinations = () => {
  return Object.values(destinations).filter(dest => dest.active);
};