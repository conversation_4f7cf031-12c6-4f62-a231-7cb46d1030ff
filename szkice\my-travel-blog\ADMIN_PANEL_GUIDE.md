# 👤 PRZEWODNIK ADMIN PANEL - Bakasana Travel Blog

## 🔐 **LOGOWANIE DO ADMIN PANEL**

### **Dostęp:**
- **URL**: `http://localhost:3002/admin` (dev) / `https://twoja-domena.com/admin` (prod)
- **Hasło**: `BakasanaAdmin2024!SecurePassword` (zmień w produkcji!)

### **Proces logowania:**
1. Wejdź na `/admin`
2. Wprowadź hasło administratora
3. Kliknij "Zaloguj się"
4. Po pomyślnym logowaniu zostaniesz przekierowany do dashboardu

---

## 🏠 **DASHBOARD - CO MOŻESZ ROBIĆ**

Po zalogowaniu masz dostęp do głównego dashboardu z następującymi funkcjami:

### **1. 📝 SANITY CMS**
- **Funkcja**: Zarządzanie treścią strony
- **Co możesz edytować**:
  - ✅ Artykuły bloga
  - ✅ Opisy programów/retreatów
  - ✅ Opinie klientów
  - ✅ FAQ (często zadawane pytania)
  - ✅ Galeria zdjęć
  - ✅ Informacje "O mnie"
- **Jak używać**: Kliknij "Otwórz CMS" → otworzy się Sanity Studio w nowej karcie

### **2. 📊 ANALYTICS**
- **Funkcja**: Statystyki i analityka strony
- **Co możesz sprawdzić**:
  - ✅ Liczba odwiedzin
  - ✅ Źródła ruchu
  - ✅ Najpopularniejsze strony
  - ✅ Konwersje (rezerwacje, newsletter)
  - ✅ Demografia użytkowników
- **Jak używać**: Kliknij "Google Analytics" → otworzy się GA w nowej karcie

### **3. 📅 REZERWACJE**
- **Funkcja**: Zarządzanie bookingami klientów
- **Co możesz robić**:
  - ✅ **Przeglądać** wszystkie rezerwacje
  - ✅ **Filtrować** po statusie (oczekujące/potwierdzone/anulowane)
  - ✅ **Zmieniać status** rezerwacji
  - ✅ **Przeglądać dane** klientów (imię, email, telefon)
  - ✅ **Czytać wiadomości** od klientów
- **Jak używać**: Kliknij "Zobacz rezerwacje"

### **4. 📧 NEWSLETTER**
- **Funkcja**: Zarządzanie subskrypcjami newsletter
- **Co możesz robić**:
  - ✅ **Przeglądać** listę subskrybentów
  - ✅ **Eksportować** adresy email
  - ✅ **Usuwać** nieaktywne subskrypcje
  - ✅ **Wysyłać** kampanie email (przez zewnętrzny serwis)
- **Jak używać**: Kliknij "Zarządzaj newsletter"

### **5. ⚙️ USTAWIENIA**
- **Funkcja**: Konfiguracja strony i systemu
- **Co możesz zmieniać**:
  - ✅ **Informacje kontaktowe**
  - ✅ **Ceny programów**
  - ✅ **Ustawienia SEO**
  - ✅ **Integracje** (Google Analytics, email)
- **Jak używać**: Kliknij "Ustawienia"

### **6. 📈 SZYBKIE STATYSTYKI**
- **Funkcja**: Przegląd kluczowych metryk
- **Co widzisz**:
  - ✅ Liczba rezerwacji
  - ✅ Subskrybenci newsletter
  - ✅ Odwiedziny strony
  - ✅ Współczynnik konwersji

---

## 📅 **ZARZĄDZANIE REZERWACJAMI - SZCZEGÓŁY**

### **Dostępne akcje:**

#### **1. Przeglądanie rezerwacji**
- **Lista wszystkich** bookingów z danymi klientów
- **Sortowanie** według daty (najnowsze pierwsze)
- **Informacje**: imię, nazwisko, email, telefon, program, destination

#### **2. Filtrowanie**
- **Wszystkie** - pokazuje wszystkie rezerwacje
- **Oczekujące** - nowe rezerwacje wymagające akcji
- **Potwierdzone** - zaakceptowane rezerwacje
- **Anulowane** - odwołane rezerwacje

#### **3. Zmiana statusu**
- **Oczekujące → Potwierdzone**: Zaakceptuj rezerwację
- **Oczekujące → Anulowane**: Odrzuć rezerwację
- **Potwierdzone → Anulowane**: Anuluj potwierdzoną rezerwację
- **Anulowane → Oczekujące**: Przywróć anulowaną rezerwację

#### **4. Szczegóły rezerwacji**
- **Dane osobowe**: Imię, nazwisko, email, telefon
- **Program**: Wybrany retreat/program
- **Destynacja**: Lokalizacja (Bali, Sri Lanka, Tajlandia)
- **Data**: Kiedy została złożona rezerwacja
- **Wiadomość**: Dodatkowe informacje od klienta

---

## 🔒 **BEZPIECZEŃSTWO ADMIN PANEL**

### **Zabezpieczenia:**
- ✅ **JWT Authentication** - bezpieczne tokeny
- ✅ **Rate limiting** - ochrona przed atakami
- ✅ **Session timeout** - automatyczne wylogowanie po 24h
- ✅ **IP tracking** - logi dostępu
- ✅ **Encrypted passwords** - hasła zabezpieczone

### **Dobre praktyki:**
- 🔐 **Zawsze się wyloguj** po zakończeniu pracy
- 🔐 **Nie udostępniaj** hasła innym osobom
- 🔐 **Używaj silnego hasła** (min. 12 znaków)
- 🔐 **Regularnie zmieniaj** hasło (co 3 miesiące)
- 🔐 **Nie loguj się** z publicznych komputerów

---

## 📱 **RESPONSYWNOŚĆ**

Admin panel jest w pełni responsywny:
- ✅ **Desktop** - pełna funkcjonalność
- ✅ **Tablet** - dostosowany layout
- ✅ **Mobile** - zoptymalizowany dla telefonu

---

## 🚀 **WORKFLOW ZARZĄDZANIA**

### **Typowy dzień administratora:**

#### **1. Rano (sprawdzenie nowych rezerwacji)**
1. Zaloguj się do admin panel
2. Przejdź do "Rezerwacje"
3. Sprawdź filtr "Oczekujące"
4. Przejrzyj nowe bookings
5. Potwierdź lub anuluj rezerwacje
6. Odpowiedz klientom email (poza systemem)

#### **2. W ciągu dnia (zarządzanie treścią)**
1. Otwórz Sanity CMS
2. Dodaj nowe artykuły bloga
3. Zaktualizuj opisy programów
4. Dodaj nowe zdjęcia do galerii
5. Odpowiedz na komentarze

#### **3. Wieczorem (analiza i planowanie)**
1. Sprawdź Google Analytics
2. Przejrzyj statystyki newsletter
3. Zaplanuj nowe kampanie
4. Przygotuj treści na następny dzień

---

## 🆘 **ROZWIĄZYWANIE PROBLEMÓW**

### **Nie mogę się zalogować:**
- Sprawdź czy hasło jest poprawne
- Wyczyść cache przeglądarki
- Sprawdź czy nie przekroczyłeś limitu prób (5 prób/15min)

### **Nie widzę nowych rezerwacji:**
- Odśwież stronę (F5)
- Sprawdź czy filtr nie jest ustawiony na "Anulowane"
- Sprawdź czy rezerwacje rzeczywiście wpłynęły

### **Sanity CMS nie działa:**
- Sprawdź czy masz poprawne uprawnienia
- Sprawdź konfigurację w `.env.local`
- Skontaktuj się z deweloperem

---

## 📞 **WSPARCIE TECHNICZNE**

W przypadku problemów technicznych:
- **Email**: <EMAIL>
- **Telefon**: +48 XXX XXX XXX
- **Dokumentacja**: Ten plik
- **Logi błędów**: Sprawdź konsolę przeglądarki (F12)

---

**Ostatnia aktualizacja**: 2024-12-19  
**Wersja admin panel**: 1.0  
**Status**: ✅ Gotowy do użytku
