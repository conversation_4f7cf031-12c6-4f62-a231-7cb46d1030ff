import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

export function middleware(request) {
  // <PERSON><PERSON>rz <PERSON>ż<PERSON>ę z URL
  const path = request.nextUrl.pathname;

  // Ochrona admin routes
  if (path.startsWith('/admin') && path !== '/admin') {
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (!token) {
      return NextResponse.redirect(new URL('/admin', request.url));
    }

    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      if (decoded.role !== 'admin') {
        return NextResponse.redirect(new URL('/admin', request.url));
      }
    } catch (error) {
      return NextResponse.redirect(new URL('/admin', request.url));
    }
  }

  // Dodaj nagłówki bezpieczeństwa
  const response = NextResponse.next();

  // Dodaj nagłówki CORS tylko dla API (z ograniczeniami)
  if (path.startsWith('/api/')) {
    // Bardziej restrykcyjne CORS
    const origin = request.headers.get('origin');
    const allowedOrigins = [
      'https://bakasana-travel.blog',
      'https://www.bakasana-travel.blog',
      'http://localhost:3002' // tylko w development
    ];

    if (allowedOrigins.includes(origin) || process.env.NODE_ENV === 'development') {
      response.headers.set('Access-Control-Allow-Origin', origin || '*');
    }

    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    response.headers.set('Access-Control-Max-Age', '86400'); // 24 godziny
  }

  // Dodaj dodatkowe nagłówki bezpieczeństwa
  response.headers.set('X-Robots-Tag', 'index, follow');
  response.headers.set('X-DNS-Prefetch-Control', 'on');

  return response;
}

// Matcher dla middleware - ochrona admin i API routes
export const config = {
  matcher: [
    '/api/:path*',
    '/admin/:path*',
    // Dodaj inne ścieżki wymagające ochrony
  ],
};