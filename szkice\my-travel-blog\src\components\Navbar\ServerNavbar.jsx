import React from 'react';

// Static navigation links to avoid import issues
const staticNavLinks = [
  { href: '/', label: 'Strona główna' },
  { href: '/blog', label: 'Blog' },
  {
    href: '/program',
    label: 'Program',
    submenu: [
      { href: '/program?destination=bali', label: 'Bali - 12 dni' },
      { href: '/program?destination=srilanka', label: 'Sri Lanka - 10 dni' }
    ]
  },
  { href: '/zajecia-online', label: 'Zajęcia Online' },
  { href: '/o-mnie', label: 'O mnie' },
  { href: '/galeria', label: 'Galeria' },
  { href: '/kontakt', label: 'Kontakt' },
];

export default function ServerNavbar() {
  return (
    <>
      <header className="bg-rice/95 backdrop-blur-md border-b border-temple/5 transition-all duration-300 h-20 md:h-20">
        <div className="container mx-auto px-4 h-full">
          <div className="flex items-center justify-between h-full">
            {/* Logo - BAKASANA w Cormorant G<PERSON>mond */}
            <a href="/" className="flex items-center">
              <span className="text-2xl font-serif font-light text-temple tracking-wider">
                BAKASANA
              </span>
            </a>

            {/* Nawigacja desktopowa - po prawej z większymi odstępami */}
            <nav className="hidden md:flex items-center space-x-8">
              {staticNavLinks.map((link) => (
                <div key={link.href} className="relative group">
                  <a
                    href={link.href}
                    className="text-sm font-light text-temple/70 hover:text-temple transition-all duration-300 px-6 py-2 tracking-wide uppercase relative group"
                  >
                    {link.label}
                    {link.submenu && (
                      <span className="ml-1 text-xs transition-transform duration-300 group-hover:rotate-180">▼</span>
                    )}
                    {/* Active indicator line */}
                    <div className="absolute bottom-0 left-1/2 w-0 h-0.5 bg-temple transition-all duration-300 group-hover:w-full group-hover:left-0"></div>
                  </a>

                  {/* Enhanced dropdown menu */}
                  {link.submenu && (
                    <div className="absolute top-full left-0 mt-2 bg-rice/95 backdrop-blur-md shadow-medium rounded-lg border border-temple/10 p-4 min-w-[220px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                      {link.submenu.map((sublink, index) => (
                        <a
                          key={sublink.href}
                          href={sublink.href}
                          className="block text-sm font-light text-temple/70 hover:text-temple hover:bg-temple/5 transition-all duration-300 py-3 px-3 rounded-md uppercase tracking-wide relative group/item"
                          style={{ animationDelay: `${index * 50}ms` }}
                        >
                          {sublink.label}
                          <div className="absolute left-0 top-1/2 w-0 h-0.5 bg-temple transition-all duration-300 group-hover/item:w-2 transform -translate-y-1/2"></div>
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* Mobilne menu - uproszczone */}
            <div className="md:hidden">
              <details className="relative">
                <summary className="text-temple cursor-pointer font-light text-sm uppercase tracking-wide">
                  Menu
                </summary>
                <nav className="absolute right-0 top-full mt-2 bg-rice/95 backdrop-blur-md shadow-medium p-6 min-w-[200px]">
                  {staticNavLinks.map((link) => (
                    <a
                      key={link.href}
                      href={link.href}
                      className="block text-sm font-light text-temple/70 hover:text-temple transition-colors py-3 uppercase tracking-wide"
                    >
                      {link.label}
                    </a>
                  ))}
                  <div className="mt-4 pt-4 border-t border-temple/10">
                    <a
                      href="tel:+48606101523"
                      className="block text-sm font-light text-temple py-2"
                    >
                      +48 606 101 523
                    </a>
                  </div>
                </nav>
              </details>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}