/**
 * Skrypt do bezpiecznego czyszczenia pamięci podręcznej Next.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { rimraf } = require('rimraf'); // Zmiana: destructuring import

// Katalogi do wyczyszczenia
const directories = [
  '.next',
  '.vercel/output',
  'node_modules/.cache'
];

async function safeClean() {
  try {
    console.log('🧹 Rozpoczynam bezpieczne czyszczenie cache...');
    
    // Sprawdź, czy istnieją procesy na porcie 3002
    try {
      // Dla Windows
      execSync('taskkill /F /IM node.exe /FI "WINDOWTITLE eq next*"', { stdio: 'ignore' });
      console.log('✅ Zatrzymano procesy Next.js');
    } catch (e) {
      console.log('ℹ️ Brak aktywnych procesów Next.js do zatrzymania');
    }
    
    // Od<PERSON>ekaj moment, aby procesy mogły się zako<PERSON>
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Wy<PERSON>yść katalogi
    for (const dir of directories) {
      const dirPath = path.join(process.cwd(), dir);
      
      if (fs.existsSync(dirPath)) {
        console.log(`🗑️ Czyszczę katalog: ${dir}`);
        try {
          await rimraf(dirPath, { maxRetries: 3, retryDelay: 1000 }); // Zmiana: await rimraf
          console.log(`✅ Katalog ${dir} wyczyszczony`);
        } catch (err) {
          console.error(`❌ Błąd podczas czyszczenia ${dir}:`, err.message);
        }
      } else {
        console.log(`ℹ️ Katalog ${dir} nie istnieje, pomijam`);
      }
    }
    
    console.log('🎉 Czyszczenie zakończone pomyślnie!');
    
  } catch (error) {
    console.error('❌ Błąd podczas czyszczenia:', error.message);
    process.exit(1);
  }
}

// Uruchom czyszczenie
safeClean();