import React from 'react';
import { Home } from 'lucide-react';
import Link from 'next/link';
import { socialLinks, studioInfo } from '../../data/contactData';
import { navigationLinks } from '../../data/navigationLinks';

export default function ServerFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer
      id="footer"
      className="pt-48 pb-12"
    >
      <div className="max-w-7xl mx-auto px-8 sm:px-12 lg:px-16">
        {/* Minimalistyczny layout - 2 kolumny */}
        <div className="grid md:grid-cols-2 gap-16 mb-16">

          {/* Lewa kolumna - duże logo */}
          <div>
            <h3 className="text-4xl md:text-5xl font-serif font-light text-temple mb-8 tracking-wide">
              BAKASANA
            </h3>
            <p className="text-temple/60 text-lg leading-loose font-light max-w-md">
              Transformacyjne retreaty jogowe na Bali z Julią Jakubowicz
            </p>
          </div>

          {/* Prawa kolumna - 3 linki */}
          <div className="flex flex-col justify-end">
            <nav aria-label="Nawigacja w stopce" className="mb-8">
              <ul className="space-y-4">
                <li>
                  <Link
                    href="/program"
                    className="text-temple/70 hover:text-temple transition-colors text-lg font-light tracking-wide"
                  >
                    Program
                  </Link>
                </li>
                <li>
                  <Link
                    href="/kontakt"
                    className="text-temple/70 hover:text-temple transition-colors text-lg font-light tracking-wide"
                  >
                    Kontakt
                  </Link>
                </li>
                <li>
                  <Link
                    href="/blog"
                    className="text-temple/70 hover:text-temple transition-colors text-lg font-light tracking-wide"
                  >
                    Blog
                  </Link>
                </li>
              </ul>
            </nav>
          </div>
        </div>

        {/* Copyright - malutki */}
        <div className="border-t border-temple/10 pt-8">
          <p className="text-temple/40 font-light tracking-wider uppercase" style={{ fontSize: '10px' }}>
            © {currentYear} Bakasana. Wszystkie prawa zastrzeżone.
          </p>
        </div>
      </div>
    </footer>
  );
}