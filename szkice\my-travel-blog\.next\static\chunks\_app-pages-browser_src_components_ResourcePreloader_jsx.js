"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_ResourcePreloader_jsx"],{

/***/ "(app-pages-browser)/./src/components/ResourcePreloader.jsx":
/*!**********************************************!*\
  !*** ./src/components/ResourcePreloader.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResourcePreloader)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nfunction ResourcePreloader() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ResourcePreloader.useEffect\": ()=>{\n            // Preload critical images with priority\n            const criticalImages = [\n                {\n                    src: '/images/background/bali-hero.webp',\n                    priority: 'high'\n                },\n                {\n                    src: '/images/profile/omnie-opt.webp',\n                    priority: 'high'\n                },\n                {\n                    src: '/images/gallery/ubud-rice-terraces.webp',\n                    priority: 'medium'\n                },\n                {\n                    src: '/images/gallery/uluwatu-temple.webp',\n                    priority: 'medium'\n                }\n            ];\n            criticalImages.forEach({\n                \"ResourcePreloader.useEffect\": (param)=>{\n                    let { src, priority } = param;\n                    const link = document.createElement('link');\n                    link.rel = 'preload';\n                    link.as = 'image';\n                    link.href = src;\n                    link.fetchPriority = priority;\n                    document.head.appendChild(link);\n                }\n            }[\"ResourcePreloader.useEffect\"]);\n            // DNS prefetch and preconnect for external domains\n            const externalConnections = [\n                {\n                    domain: 'https://fonts.googleapis.com',\n                    type: 'preconnect'\n                },\n                {\n                    domain: 'https://fonts.gstatic.com',\n                    type: 'preconnect',\n                    crossorigin: true\n                },\n                {\n                    domain: 'https://images.unsplash.com',\n                    type: 'dns-prefetch'\n                },\n                {\n                    domain: 'https://api.mapbox.com',\n                    type: 'dns-prefetch'\n                },\n                {\n                    domain: 'https://events.mapbox.com',\n                    type: 'dns-prefetch'\n                },\n                {\n                    domain: 'https://vitals.vercel-insights.com',\n                    type: 'dns-prefetch'\n                },\n                {\n                    domain: 'https://va.vercel-scripts.com',\n                    type: 'dns-prefetch'\n                }\n            ];\n            externalConnections.forEach({\n                \"ResourcePreloader.useEffect\": (param)=>{\n                    let { domain, type, crossorigin } = param;\n                    const link = document.createElement('link');\n                    link.rel = type;\n                    link.href = domain;\n                    if (crossorigin) link.crossOrigin = 'anonymous';\n                    document.head.appendChild(link);\n                }\n            }[\"ResourcePreloader.useEffect\"]);\n            // Prefetch next likely pages\n            const prefetchPages = [\n                '/blog',\n                '/program',\n                '/o-mnie',\n                '/rezerwacja',\n                '/mapa',\n                '/galeria'\n            ];\n            // Delay prefetch to not interfere with critical loading\n            setTimeout({\n                \"ResourcePreloader.useEffect\": ()=>{\n                    prefetchPages.forEach({\n                        \"ResourcePreloader.useEffect\": (href)=>{\n                            const link = document.createElement('link');\n                            link.rel = 'prefetch';\n                            link.href = href;\n                            document.head.appendChild(link);\n                        }\n                    }[\"ResourcePreloader.useEffect\"]);\n                }\n            }[\"ResourcePreloader.useEffect\"], 2000);\n            // Preload service worker\n            if ('serviceWorker' in navigator) {\n                const link = document.createElement('link');\n                link.rel = 'preload';\n                link.as = 'script';\n                link.href = '/sw.js';\n                document.head.appendChild(link);\n            }\n            // Performance monitoring for Core Web Vitals\n            if ( true && 'PerformanceObserver' in window) {\n                try {\n                    const observer = new PerformanceObserver({\n                        \"ResourcePreloader.useEffect\": (list)=>{\n                            list.getEntries().forEach({\n                                \"ResourcePreloader.useEffect\": (entry)=>{\n                                    // Log performance metrics in development\n                                    if (true) {\n                                        if (entry.entryType === 'largest-contentful-paint') {\n                                            console.log('LCP:', entry.startTime);\n                                        }\n                                        if (entry.entryType === 'first-input') {\n                                            console.log('FID:', entry.processingStart - entry.startTime);\n                                        }\n                                        if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {\n                                            console.log('CLS:', entry.value);\n                                        }\n                                    }\n                                }\n                            }[\"ResourcePreloader.useEffect\"]);\n                        }\n                    }[\"ResourcePreloader.useEffect\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'largest-contentful-paint',\n                            'first-input',\n                            'layout-shift'\n                        ]\n                    });\n                } catch (e) {\n                    // Fallback for older browsers\n                    console.log('Performance Observer not fully supported');\n                }\n            }\n        }\n    }[\"ResourcePreloader.useEffect\"], []);\n    return null;\n}\n_s(ResourcePreloader, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = ResourcePreloader;\nvar _c;\n$RefreshReg$(_c, \"ResourcePreloader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Jlc291cmNlUHJlbG9hZGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRWtDO0FBRW5CLFNBQVNDOztJQUN0QkQsZ0RBQVNBO3VDQUFDO1lBQ1Isd0NBQXdDO1lBQ3hDLE1BQU1FLGlCQUFpQjtnQkFDckI7b0JBQUVDLEtBQUs7b0JBQXFDQyxVQUFVO2dCQUFPO2dCQUM3RDtvQkFBRUQsS0FBSztvQkFBa0NDLFVBQVU7Z0JBQU87Z0JBQzFEO29CQUFFRCxLQUFLO29CQUEyQ0MsVUFBVTtnQkFBUztnQkFDckU7b0JBQUVELEtBQUs7b0JBQXVDQyxVQUFVO2dCQUFTO2FBQ2xFO1lBRURGLGVBQWVHLE9BQU87K0NBQUM7d0JBQUMsRUFBRUYsR0FBRyxFQUFFQyxRQUFRLEVBQUU7b0JBQ3ZDLE1BQU1FLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztvQkFDcENGLEtBQUtHLEdBQUcsR0FBRztvQkFDWEgsS0FBS0ksRUFBRSxHQUFHO29CQUNWSixLQUFLSyxJQUFJLEdBQUdSO29CQUNaRyxLQUFLTSxhQUFhLEdBQUdSO29CQUNyQkcsU0FBU00sSUFBSSxDQUFDQyxXQUFXLENBQUNSO2dCQUM1Qjs7WUFFQSxtREFBbUQ7WUFDbkQsTUFBTVMsc0JBQXNCO2dCQUMxQjtvQkFBRUMsUUFBUTtvQkFBZ0NDLE1BQU07Z0JBQWE7Z0JBQzdEO29CQUFFRCxRQUFRO29CQUE2QkMsTUFBTTtvQkFBY0MsYUFBYTtnQkFBSztnQkFDN0U7b0JBQUVGLFFBQVE7b0JBQStCQyxNQUFNO2dCQUFlO2dCQUM5RDtvQkFBRUQsUUFBUTtvQkFBMEJDLE1BQU07Z0JBQWU7Z0JBQ3pEO29CQUFFRCxRQUFRO29CQUE2QkMsTUFBTTtnQkFBZTtnQkFDNUQ7b0JBQUVELFFBQVE7b0JBQXNDQyxNQUFNO2dCQUFlO2dCQUNyRTtvQkFBRUQsUUFBUTtvQkFBaUNDLE1BQU07Z0JBQWU7YUFDakU7WUFFREYsb0JBQW9CVixPQUFPOytDQUFDO3dCQUFDLEVBQUVXLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxXQUFXLEVBQUU7b0JBQ3hELE1BQU1aLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztvQkFDcENGLEtBQUtHLEdBQUcsR0FBR1E7b0JBQ1hYLEtBQUtLLElBQUksR0FBR0s7b0JBQ1osSUFBSUUsYUFBYVosS0FBS2EsV0FBVyxHQUFHO29CQUNwQ1osU0FBU00sSUFBSSxDQUFDQyxXQUFXLENBQUNSO2dCQUM1Qjs7WUFFQSw2QkFBNkI7WUFDN0IsTUFBTWMsZ0JBQWdCO2dCQUNwQjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBRUQsd0RBQXdEO1lBQ3hEQzsrQ0FBVztvQkFDVEQsY0FBY2YsT0FBTzt1REFBQ00sQ0FBQUE7NEJBQ3BCLE1BQU1MLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQzs0QkFDcENGLEtBQUtHLEdBQUcsR0FBRzs0QkFDWEgsS0FBS0ssSUFBSSxHQUFHQTs0QkFDWkosU0FBU00sSUFBSSxDQUFDQyxXQUFXLENBQUNSO3dCQUM1Qjs7Z0JBQ0Y7OENBQUc7WUFFSCx5QkFBeUI7WUFDekIsSUFBSSxtQkFBbUJnQixXQUFXO2dCQUNoQyxNQUFNaEIsT0FBT0MsU0FBU0MsYUFBYSxDQUFDO2dCQUNwQ0YsS0FBS0csR0FBRyxHQUFHO2dCQUNYSCxLQUFLSSxFQUFFLEdBQUc7Z0JBQ1ZKLEtBQUtLLElBQUksR0FBRztnQkFDWkosU0FBU00sSUFBSSxDQUFDQyxXQUFXLENBQUNSO1lBQzVCO1lBRUEsNkNBQTZDO1lBQzdDLElBQUksS0FBNkIsSUFBSSx5QkFBeUJpQixRQUFRO2dCQUNwRSxJQUFJO29CQUNGLE1BQU1DLFdBQVcsSUFBSUM7dURBQW9CLENBQUNDOzRCQUN4Q0EsS0FBS0MsVUFBVSxHQUFHdEIsT0FBTzsrREFBQyxDQUFDdUI7b0NBQ3pCLHlDQUF5QztvQ0FDekMsSUFBSUMsSUFBc0MsRUFBRTt3Q0FDMUMsSUFBSUQsTUFBTUUsU0FBUyxLQUFLLDRCQUE0Qjs0Q0FDbERDLFFBQVFDLEdBQUcsQ0FBQyxRQUFRSixNQUFNSyxTQUFTO3dDQUNyQzt3Q0FDQSxJQUFJTCxNQUFNRSxTQUFTLEtBQUssZUFBZTs0Q0FDckNDLFFBQVFDLEdBQUcsQ0FBQyxRQUFRSixNQUFNTSxlQUFlLEdBQUdOLE1BQU1LLFNBQVM7d0NBQzdEO3dDQUNBLElBQUlMLE1BQU1FLFNBQVMsS0FBSyxrQkFBa0IsQ0FBQ0YsTUFBTU8sY0FBYyxFQUFFOzRDQUMvREosUUFBUUMsR0FBRyxDQUFDLFFBQVFKLE1BQU1RLEtBQUs7d0NBQ2pDO29DQUNGO2dDQUNGOzt3QkFDRjs7b0JBRUFaLFNBQVNhLE9BQU8sQ0FBQzt3QkFDZkMsWUFBWTs0QkFBQzs0QkFBNEI7NEJBQWU7eUJBQWU7b0JBQ3pFO2dCQUNGLEVBQUUsT0FBT0MsR0FBRztvQkFDViw4QkFBOEI7b0JBQzlCUixRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7WUFDRjtRQUVGO3NDQUFHLEVBQUU7SUFFTCxPQUFPO0FBQ1Q7R0FuR3dCL0I7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxibG9nX3Byb2RcXHN6a2ljZVxcbXktdHJhdmVsLWJsb2dcXHNyY1xcY29tcG9uZW50c1xcUmVzb3VyY2VQcmVsb2FkZXIuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSZXNvdXJjZVByZWxvYWRlcigpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBQcmVsb2FkIGNyaXRpY2FsIGltYWdlcyB3aXRoIHByaW9yaXR5XG4gICAgY29uc3QgY3JpdGljYWxJbWFnZXMgPSBbXG4gICAgICB7IHNyYzogJy9pbWFnZXMvYmFja2dyb3VuZC9iYWxpLWhlcm8ud2VicCcsIHByaW9yaXR5OiAnaGlnaCcgfSxcbiAgICAgIHsgc3JjOiAnL2ltYWdlcy9wcm9maWxlL29tbmllLW9wdC53ZWJwJywgcHJpb3JpdHk6ICdoaWdoJyB9LFxuICAgICAgeyBzcmM6ICcvaW1hZ2VzL2dhbGxlcnkvdWJ1ZC1yaWNlLXRlcnJhY2VzLndlYnAnLCBwcmlvcml0eTogJ21lZGl1bScgfSxcbiAgICAgIHsgc3JjOiAnL2ltYWdlcy9nYWxsZXJ5L3VsdXdhdHUtdGVtcGxlLndlYnAnLCBwcmlvcml0eTogJ21lZGl1bScgfVxuICAgIF07XG5cbiAgICBjcml0aWNhbEltYWdlcy5mb3JFYWNoKCh7IHNyYywgcHJpb3JpdHkgfSkgPT4ge1xuICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2xpbmsnKTtcbiAgICAgIGxpbmsucmVsID0gJ3ByZWxvYWQnO1xuICAgICAgbGluay5hcyA9ICdpbWFnZSc7XG4gICAgICBsaW5rLmhyZWYgPSBzcmM7XG4gICAgICBsaW5rLmZldGNoUHJpb3JpdHkgPSBwcmlvcml0eTtcbiAgICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQobGluayk7XG4gICAgfSk7XG5cbiAgICAvLyBETlMgcHJlZmV0Y2ggYW5kIHByZWNvbm5lY3QgZm9yIGV4dGVybmFsIGRvbWFpbnNcbiAgICBjb25zdCBleHRlcm5hbENvbm5lY3Rpb25zID0gW1xuICAgICAgeyBkb21haW46ICdodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tJywgdHlwZTogJ3ByZWNvbm5lY3QnIH0sXG4gICAgICB7IGRvbWFpbjogJ2h0dHBzOi8vZm9udHMuZ3N0YXRpYy5jb20nLCB0eXBlOiAncHJlY29ubmVjdCcsIGNyb3Nzb3JpZ2luOiB0cnVlIH0sXG4gICAgICB7IGRvbWFpbjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbScsIHR5cGU6ICdkbnMtcHJlZmV0Y2gnIH0sXG4gICAgICB7IGRvbWFpbjogJ2h0dHBzOi8vYXBpLm1hcGJveC5jb20nLCB0eXBlOiAnZG5zLXByZWZldGNoJyB9LFxuICAgICAgeyBkb21haW46ICdodHRwczovL2V2ZW50cy5tYXBib3guY29tJywgdHlwZTogJ2Rucy1wcmVmZXRjaCcgfSxcbiAgICAgIHsgZG9tYWluOiAnaHR0cHM6Ly92aXRhbHMudmVyY2VsLWluc2lnaHRzLmNvbScsIHR5cGU6ICdkbnMtcHJlZmV0Y2gnIH0sXG4gICAgICB7IGRvbWFpbjogJ2h0dHBzOi8vdmEudmVyY2VsLXNjcmlwdHMuY29tJywgdHlwZTogJ2Rucy1wcmVmZXRjaCcgfVxuICAgIF07XG5cbiAgICBleHRlcm5hbENvbm5lY3Rpb25zLmZvckVhY2goKHsgZG9tYWluLCB0eXBlLCBjcm9zc29yaWdpbiB9KSA9PiB7XG4gICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnbGluaycpO1xuICAgICAgbGluay5yZWwgPSB0eXBlO1xuICAgICAgbGluay5ocmVmID0gZG9tYWluO1xuICAgICAgaWYgKGNyb3Nzb3JpZ2luKSBsaW5rLmNyb3NzT3JpZ2luID0gJ2Fub255bW91cyc7XG4gICAgICBkb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKGxpbmspO1xuICAgIH0pO1xuXG4gICAgLy8gUHJlZmV0Y2ggbmV4dCBsaWtlbHkgcGFnZXNcbiAgICBjb25zdCBwcmVmZXRjaFBhZ2VzID0gW1xuICAgICAgJy9ibG9nJyxcbiAgICAgICcvcHJvZ3JhbScsXG4gICAgICAnL28tbW5pZScsXG4gICAgICAnL3JlemVyd2FjamEnLFxuICAgICAgJy9tYXBhJyxcbiAgICAgICcvZ2FsZXJpYSdcbiAgICBdO1xuXG4gICAgLy8gRGVsYXkgcHJlZmV0Y2ggdG8gbm90IGludGVyZmVyZSB3aXRoIGNyaXRpY2FsIGxvYWRpbmdcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHByZWZldGNoUGFnZXMuZm9yRWFjaChocmVmID0+IHtcbiAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2xpbmsnKTtcbiAgICAgICAgbGluay5yZWwgPSAncHJlZmV0Y2gnO1xuICAgICAgICBsaW5rLmhyZWYgPSBocmVmO1xuICAgICAgICBkb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKGxpbmspO1xuICAgICAgfSk7XG4gICAgfSwgMjAwMCk7XG5cbiAgICAvLyBQcmVsb2FkIHNlcnZpY2Ugd29ya2VyXG4gICAgaWYgKCdzZXJ2aWNlV29ya2VyJyBpbiBuYXZpZ2F0b3IpIHtcbiAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdsaW5rJyk7XG4gICAgICBsaW5rLnJlbCA9ICdwcmVsb2FkJztcbiAgICAgIGxpbmsuYXMgPSAnc2NyaXB0JztcbiAgICAgIGxpbmsuaHJlZiA9ICcvc3cuanMnO1xuICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChsaW5rKTtcbiAgICB9XG5cbiAgICAvLyBQZXJmb3JtYW5jZSBtb25pdG9yaW5nIGZvciBDb3JlIFdlYiBWaXRhbHNcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgJ1BlcmZvcm1hbmNlT2JzZXJ2ZXInIGluIHdpbmRvdykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgb2JzZXJ2ZXIgPSBuZXcgUGVyZm9ybWFuY2VPYnNlcnZlcigobGlzdCkgPT4ge1xuICAgICAgICAgIGxpc3QuZ2V0RW50cmllcygpLmZvckVhY2goKGVudHJ5KSA9PiB7XG4gICAgICAgICAgICAvLyBMb2cgcGVyZm9ybWFuY2UgbWV0cmljcyBpbiBkZXZlbG9wbWVudFxuICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XG4gICAgICAgICAgICAgIGlmIChlbnRyeS5lbnRyeVR5cGUgPT09ICdsYXJnZXN0LWNvbnRlbnRmdWwtcGFpbnQnKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0xDUDonLCBlbnRyeS5zdGFydFRpbWUpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGlmIChlbnRyeS5lbnRyeVR5cGUgPT09ICdmaXJzdC1pbnB1dCcpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnRklEOicsIGVudHJ5LnByb2Nlc3NpbmdTdGFydCAtIGVudHJ5LnN0YXJ0VGltZSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgaWYgKGVudHJ5LmVudHJ5VHlwZSA9PT0gJ2xheW91dC1zaGlmdCcgJiYgIWVudHJ5LmhhZFJlY2VudElucHV0KSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0NMUzonLCBlbnRyeS52YWx1ZSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgb2JzZXJ2ZXIub2JzZXJ2ZSh7XG4gICAgICAgICAgZW50cnlUeXBlczogWydsYXJnZXN0LWNvbnRlbnRmdWwtcGFpbnQnLCAnZmlyc3QtaW5wdXQnLCAnbGF5b3V0LXNoaWZ0J11cbiAgICAgICAgfSk7XG4gICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgIC8vIEZhbGxiYWNrIGZvciBvbGRlciBicm93c2Vyc1xuICAgICAgICBjb25zb2xlLmxvZygnUGVyZm9ybWFuY2UgT2JzZXJ2ZXIgbm90IGZ1bGx5IHN1cHBvcnRlZCcpO1xuICAgICAgfVxuICAgIH1cblxuICB9LCBbXSk7XG5cbiAgcmV0dXJuIG51bGw7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiUmVzb3VyY2VQcmVsb2FkZXIiLCJjcml0aWNhbEltYWdlcyIsInNyYyIsInByaW9yaXR5IiwiZm9yRWFjaCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJyZWwiLCJhcyIsImhyZWYiLCJmZXRjaFByaW9yaXR5IiwiaGVhZCIsImFwcGVuZENoaWxkIiwiZXh0ZXJuYWxDb25uZWN0aW9ucyIsImRvbWFpbiIsInR5cGUiLCJjcm9zc29yaWdpbiIsImNyb3NzT3JpZ2luIiwicHJlZmV0Y2hQYWdlcyIsInNldFRpbWVvdXQiLCJuYXZpZ2F0b3IiLCJ3aW5kb3ciLCJvYnNlcnZlciIsIlBlcmZvcm1hbmNlT2JzZXJ2ZXIiLCJsaXN0IiwiZ2V0RW50cmllcyIsImVudHJ5IiwicHJvY2VzcyIsImVudHJ5VHlwZSIsImNvbnNvbGUiLCJsb2ciLCJzdGFydFRpbWUiLCJwcm9jZXNzaW5nU3RhcnQiLCJoYWRSZWNlbnRJbnB1dCIsInZhbHVlIiwib2JzZXJ2ZSIsImVudHJ5VHlwZXMiLCJlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ResourcePreloader.jsx\n"));

/***/ })

}]);