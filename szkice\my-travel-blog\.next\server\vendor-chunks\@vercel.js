"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel";
exports.ids = ["vendor-chunks/@vercel"];
exports.modules = {

/***/ "(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ Analytics),
/* harmony export */   track: () => (/* binding */ track)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const Analytics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Analytics() from the server but Analytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\@vercel\\analytics\\dist\\react\\index.mjs",
"Analytics",
);const track = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call track() from the server but track is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\@vercel\\analytics\\dist\\react\\index.mjs",
"track",
);

/***/ }),

/***/ "(rsc)/./node_modules/@vercel/speed-insights/dist/react/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/react/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights),
/* harmony export */   computeRoute: () => (/* binding */ computeRoute)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const SpeedInsights = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SpeedInsights() from the server but SpeedInsights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\@vercel\\speed-insights\\dist\\react\\index.mjs",
"SpeedInsights",
);const computeRoute = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call computeRoute() from the server but computeRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\@vercel\\speed-insights\\dist\\react\\index.mjs",
"computeRoute",
);

/***/ }),

/***/ "(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics),\n/* harmony export */   track: () => (/* binding */ track)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,track auto */ // src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/analytics\";\nvar version = \"1.5.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.va) return;\n    window.va = function a(...params) {\n        (window.vaq = window.vaq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction setMode(mode = \"auto\") {\n    if (mode === \"auto\") {\n        window.vam = detectEnvironment();\n        return;\n    }\n    window.vam = mode;\n}\nfunction getMode() {\n    const mode = isBrowser() ? window.vam : detectEnvironment();\n    return mode || \"production\";\n}\nfunction isProduction() {\n    return getMode() === \"production\";\n}\nfunction isDevelopment() {\n    return getMode() === \"development\";\n}\nfunction removeKey(key, { [key]: _, ...rest }) {\n    return rest;\n}\nfunction parseProperties(properties, options) {\n    if (!properties) return void 0;\n    let props = properties;\n    const errorProperties = [];\n    for (const [key, value] of Object.entries(properties)){\n        if (typeof value === \"object\" && value !== null) {\n            if (options.strip) {\n                props = removeKey(key, props);\n            } else {\n                errorProperties.push(key);\n            }\n        }\n    }\n    if (errorProperties.length > 0 && !options.strip) {\n        throw Error(`The following properties are not valid: ${errorProperties.join(\", \")}. Only strings, numbers, booleans, and null are allowed.`);\n    }\n    return props;\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/script.debug.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/insights/script.js`;\n    }\n    return \"/_vercel/insights/script.js\";\n}\n// src/generic.ts\nfunction inject(props = {\n    debug: true\n}) {\n    var _a;\n    if (!isBrowser()) return;\n    setMode(props.mode);\n    initQueue();\n    if (props.beforeSend) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.disableAutoTrack) {\n        script.dataset.disableAutoTrack = \"1\";\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/insights`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    script.onerror = ()=>{\n        const errorMessage = isDevelopment() ? \"Please check if any ad blockers are enabled and try again.\" : \"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.\";\n        console.log(`[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`);\n    };\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    document.head.appendChild(script);\n}\nfunction track(name2, properties, options) {\n    var _a, _b;\n    if (!isBrowser()) {\n        const msg = \"[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment\";\n        if (isProduction()) {\n            console.warn(msg);\n        } else {\n            throw new Error(msg);\n        }\n        return;\n    }\n    if (!properties) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"event\", {\n            name: name2,\n            options\n        });\n        return;\n    }\n    try {\n        const props = parseProperties(properties, {\n            strip: isProduction()\n        });\n        (_b = window.va) == null ? void 0 : _b.call(window, \"event\", {\n            name: name2,\n            data: props,\n            options\n        });\n    } catch (err) {\n        if (err instanceof Error && isDevelopment()) {\n            console.error(err);\n        }\n    }\n}\nfunction pageview({ route, path }) {\n    var _a;\n    (_a = window.va) == null ? void 0 : _a.call(window, \"pageview\", {\n        route,\n        path\n    });\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction Analytics(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Analytics.useEffect\": ()=>{\n            var _a;\n            if (props.beforeSend) {\n                (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n            }\n        }\n    }[\"Analytics.useEffect\"], [\n        props.beforeSend\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Analytics.useEffect\": ()=>{\n            inject({\n                framework: props.framework || \"react\",\n                basePath: props.basePath ?? getBasePath(),\n                ...props.route !== void 0 && {\n                    disableAutoTrack: true\n                },\n                ...props\n            });\n        }\n    }[\"Analytics.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Analytics.useEffect\": ()=>{\n            if (props.route && props.path) {\n                pageview({\n                    route: props.route,\n                    path: props.path\n                });\n            }\n        }\n    }[\"Analytics.useEffect\"], [\n        props.route,\n        props.path\n    ]);\n    return null;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@vercel/speed-insights/dist/react/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/react/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights),\n/* harmony export */   computeRoute: () => (/* binding */ computeRoute)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ SpeedInsights,computeRoute auto */ // src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/speed-insights\";\nvar version = \"1.2.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.si) return;\n    window.si = function a(...params) {\n        (window.siq = window.siq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction isDevelopment() {\n    return detectEnvironment() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[${key}]`);\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[...${key}]`);\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js\";\n    }\n    if (props.dsn) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/speed-insights/script.js`;\n    }\n    return \"/_vercel/speed-insights/script.js\";\n}\n// src/generic.ts\nfunction injectSpeedInsights(props = {}) {\n    var _a;\n    if (!isBrowser() || props.route === null) return null;\n    initQueue();\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n    if (props.beforeSend) {\n        (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.sampleRate) {\n        script.dataset.sampleRate = props.sampleRate.toString();\n    }\n    if (props.route) {\n        script.dataset.route = props.route;\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    script.onerror = ()=>{\n        console.log(`[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`);\n    };\n    document.head.appendChild(script);\n    return {\n        setRoute: (route)=>{\n            script.dataset.route = route ?? void 0;\n        }\n    };\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction SpeedInsights(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SpeedInsights.useEffect\": ()=>{\n            var _a;\n            if (props.beforeSend) {\n                (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n            }\n        }\n    }[\"SpeedInsights.useEffect\"], [\n        props.beforeSend\n    ]);\n    const setScriptRoute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SpeedInsights.useEffect\": ()=>{\n            if (!setScriptRoute.current) {\n                const script = injectSpeedInsights({\n                    framework: props.framework ?? \"react\",\n                    basePath: props.basePath ?? getBasePath(),\n                    ...props\n                });\n                if (script) {\n                    setScriptRoute.current = script.setRoute;\n                }\n            } else if (props.route) {\n                setScriptRoute.current(props.route);\n            }\n        }\n    }[\"SpeedInsights.useEffect\"], [\n        props.route\n    ]);\n    return null;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/speed-insights/dist/react/index.mjs\n");

/***/ })

};
;