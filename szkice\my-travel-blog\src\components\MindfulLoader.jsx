'use client';

import { useState, useEffect } from 'react';

export default function MindfulLoader({ isLoading = false, text = "Oddychaj..." }) {
  const [show, setShow] = useState(isLoading);

  useEffect(() => {
    if (isLoading) {
      setShow(true);
    } else {
      // Fade out delay
      const timer = setTimeout(() => setShow(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isLoading]);

  if (!show) return null;

  return (
    <div className={`mindful-loader ${!isLoading ? 'opacity-0' : ''}`}>
      <div className="breath-circle"></div>
      <p className="breath-text">{text}</p>
    </div>
  );
}

// Hook dla łatwego użycia
export function useMindfulLoader() {
  const [isLoading, setIsLoading] = useState(false);

  const startLoading = (text) => {
    setIsLoading(true);
    return text;
  };

  const stopLoading = () => {
    setIsLoading(false);
  };

  return {
    isLoading,
    startLoading,
    stopLoading,
    MindfulLoader: ({ text }) => <MindfulLoader isLoading={isLoading} text={text} />
  };
}
