'use client';

import React, { useEffect, useState } from 'react';

const PerformanceOptimizer = ({ children }) => {
  const [isLowPerformance, setIsLowPerformance] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [connectionSpeed, setConnectionSpeed] = useState('fast');

  useEffect(() => {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setReducedMotion(mediaQuery.matches);
    
    const handleChange = (e) => setReducedMotion(e.matches);
    mediaQuery.addEventListener('change', handleChange);

    // Detect device performance
    const detectPerformance = () => {
      // Check device memory (if available)
      const deviceMemory = navigator.deviceMemory || 4;
      
      // Check hardware concurrency
      const cores = navigator.hardwareConcurrency || 2;
      
      // Check connection speed
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      if (connection) {
        const effectiveType = connection.effectiveType;
        setConnectionSpeed(effectiveType);
        
        // Consider slow if 2g or slow-2g
        if (effectiveType === '2g' || effectiveType === 'slow-2g') {
          setIsLowPerformance(true);
        }
      }
      
      // Consider low performance if less than 2GB RAM or 2 cores
      if (deviceMemory < 2 || cores < 2) {
        setIsLowPerformance(true);
      }
      
      // Check if device is mobile with limited resources
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      if (isMobile && (deviceMemory < 4 || cores < 4)) {
        setIsLowPerformance(true);
      }
    };

    detectPerformance();

    // Monitor frame rate
    let frameCount = 0;
    let lastTime = performance.now();
    
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = frameCount;
        frameCount = 0;
        lastTime = currentTime;
        
        // If FPS is consistently low, enable performance mode
        if (fps < 30) {
          setIsLowPerformance(true);
        }
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  useEffect(() => {
    // Apply performance optimizations
    const root = document.documentElement;
    
    if (isLowPerformance || reducedMotion) {
      // Disable heavy animations
      root.style.setProperty('--animation-duration', '0.1s');
      root.style.setProperty('--transition-duration', '0.1s');
      
      // Reduce particle counts
      root.style.setProperty('--particle-count', '5');
      
      // Disable parallax on low-performance devices
      root.classList.add('performance-mode');
      
      // Reduce backdrop blur
      root.style.setProperty('--backdrop-blur', '2px');
    } else {
      // Normal performance
      root.style.setProperty('--animation-duration', '1s');
      root.style.setProperty('--transition-duration', '0.3s');
      root.style.setProperty('--particle-count', '20');
      root.classList.remove('performance-mode');
      root.style.setProperty('--backdrop-blur', '12px');
    }

    // Connection-based optimizations
    if (connectionSpeed === '2g' || connectionSpeed === 'slow-2g') {
      // Disable video backgrounds
      root.classList.add('no-video');
      
      // Reduce image quality
      root.classList.add('low-bandwidth');
    }
  }, [isLowPerformance, reducedMotion, connectionSpeed]);

  // Intersection Observer for lazy loading animations
  useEffect(() => {
    const observerOptions = {
      threshold: isLowPerformance ? 0.5 : 0.1,
      rootMargin: isLowPerformance ? '50px' : '100px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // Observe all elements with animation classes
    const animatedElements = document.querySelectorAll('[class*="animate-"], [class*="motion-"]');
    animatedElements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, [isLowPerformance]);

  // Preload critical resources
  useEffect(() => {
    if (!isLowPerformance && connectionSpeed !== '2g') {
      // Preload next page resources
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = '/program';
      document.head.appendChild(link);

      // Preload critical images
      const criticalImages = [
        '/images/background/bali-hero.webp',
        '/images/blog/handstand-828.webp'
      ];

      criticalImages.forEach((src) => {
        const img = new Image();
        img.src = src;
      });
    }
  }, [isLowPerformance, connectionSpeed]);

  return (
    <>
      {children}
      
      {/* Performance indicator for development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-4 left-4 bg-black/80 text-white p-2 rounded text-xs z-50">
          <div>Performance: {isLowPerformance ? 'Low' : 'Normal'}</div>
          <div>Motion: {reducedMotion ? 'Reduced' : 'Normal'}</div>
          <div>Connection: {connectionSpeed}</div>
        </div>
      )}
    </>
  );
};

// CSS for performance mode
const performanceModeStyles = `
  .performance-mode .chakra-parallax {
    transform: none !important;
  }
  
  .performance-mode [class*="animate-"] {
    animation-duration: 0.1s !important;
  }
  
  .performance-mode [class*="transition-"] {
    transition-duration: 0.1s !important;
  }
  
  .performance-mode .floating-particles {
    display: none !important;
  }
  
  .performance-mode .backdrop-blur {
    backdrop-filter: blur(2px) !important;
  }
  
  .no-video video {
    display: none !important;
  }
  
  .low-bandwidth img {
    filter: contrast(0.9) brightness(0.95);
  }
  
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
    
    .floating-particles,
    .breathing-animation,
    .parallax-layer {
      display: none !important;
    }
  }
`;

// Inject performance styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = performanceModeStyles;
  document.head.appendChild(styleSheet);
}

export default PerformanceOptimizer;
