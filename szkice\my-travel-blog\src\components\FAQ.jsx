'use client';

import { useState } from 'react';
import OptimizedIcon from './OptimizedIcon';

const FAQ = ({ faqs, title = "Często Zadawane Pytania" }) => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  // Structured Data dla FAQ
  const faqStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };

  return (
    <section className="py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-serif text-temple mb-4">{title}</h2>
          <p className="text-wood-light/80 text-lg">Znajdź odpowiedzi na najczęściej zadawane pytania</p>
          <div className="w-24 h-px bg-temple/20 mx-auto mt-6"></div>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="bg-shell/60 backdrop-blur-sm rounded-2xl border border-temple/10 overflow-hidden transition-all duration-300 hover:shadow-medium"
            >
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full px-6 py-5 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-temple/20 focus:ring-inset"
                aria-expanded={openIndex === index}
              >
                <h3 className="text-lg font-medium text-temple pr-4">
                  {faq.question}
                </h3>
                <OptimizedIcon
                  name="ChevronDown"
                  className={`w-5 h-5 text-temple/60 transition-transform duration-300 flex-shrink-0 ${
                    openIndex === index ? 'rotate-180' : ''
                  }`}
                />
              </button>
              
              <div
                className={`overflow-hidden transition-all duration-300 ${
                  openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}
              >
                <div className="px-6 pb-5 pt-2">
                  <div className="prose prose-sm max-w-none text-wood-light/80 leading-relaxed">
                    <div dangerouslySetInnerHTML={{ __html: faq.answer }} />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />
    </section>
  );
};

// Domyślne FAQ dla retreatów jogowych
export const defaultYogaFAQs = [
  {
    question: "Czy retreat jest odpowiedni dla początkujących?",
    answer: "Tak! Nasze retreaty są dostosowane do wszystkich poziomów zaawansowania. Julia prowadzi zajęcia z uwzględnieniem indywidualnych potrzeb każdego uczestnika. Oferujemy modyfikacje pozycji i alternatywne warianty dla początkujących."
  },
  {
    question: "Co jest wliczone w cenę retreatu?",
    answer: "W cenę wliczone są: zakwaterowanie, wszystkie posiłki, codzienne zajęcia jogi i medytacji, warsztaty, wycieczki zgodnie z programem, transport lokalny na Bali oraz opieka instruktora przez cały pobyt."
  },
  {
    question: "Jakie dokumenty są potrzebne do wyjazdu na Bali?",
    answer: "Do wyjazdu na Bali potrzebujesz ważnego paszportu (minimum 6 miesięcy ważności) oraz wizy turystycznej, którą można uzyskać po przylocie lub online przed wyjazdem. Zalecamy również ubezpieczenie podróżne."
  },
  {
    question: "Czy mogę uczestniczyć w retreatie będąc w ciąży?",
    answer: "Tak, ale wymagana jest wcześniejsza konsultacja z Julią. Jako fizjoterapeutka dostosuje program do Twoich potrzeb, oferując bezpieczne modyfikacje pozycji i specjalne zajęcia prenatalne."
  },
  {
    question: "Jaka jest polityka anulowania?",
    answer: "Anulowanie do 60 dni przed wyjazdem - zwrot 100% wpłaconej kwoty. Anulowanie 30-59 dni przed - zwrot 50%. Anulowanie poniżej 30 dni - brak zwrotu, chyba że znajdziemy zastępstwo."
  },
  {
    question: "Czy potrzebuję własnej maty do jogi?",
    answer: "Nie, zapewniamy wszystkie niezbędne akcesoria do praktyki jogi, w tym maty, bloki, pasy i koce. Możesz jednak zabrać swoją matę, jeśli preferujesz praktykę na własnym sprzęcie."
  }
];

export default FAQ;
