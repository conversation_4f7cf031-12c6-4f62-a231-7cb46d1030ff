'use client';

import React from 'react';

export default class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    // Możesz logować błędy tutaj, np. do konsoli lub do serwisu logowania.
  }
  
  render() {
    if (this.state.hasError) {
      return <div>Co<PERSON> poszło nie tak.</div>;
    }
    return this.props.children;
  }
}