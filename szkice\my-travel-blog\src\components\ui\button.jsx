'use client';
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva } from "class-variance-authority";
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-light transition-all duration-300 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-offset-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        primary: "bg-temple/90 text-rice hover:bg-temple transition-colors focus:ring-temple/30",
        secondary: "bg-rice text-temple border border-temple/10 hover:bg-rice/80 transition-colors focus:ring-temple/20",
        outline: "bg-transparent border border-temple/20 text-temple hover:bg-temple/5 transition-colors focus:ring-temple/20",
        ghost: "bg-transparent text-temple hover:bg-temple/5 transition-colors focus:ring-temple/20",
        hero: "bg-white/10 backdrop-blur-sm text-temple/85 border border-white/10 hover:bg-white/20 hover:-translate-y-0.25 transition-all focus:ring-white/20",
        golden: "bg-golden/90 text-rice hover:bg-golden transition-colors focus:ring-golden/30",
      },
      size: {
        sm: "px-3 py-1.5 text-xs",
        default: "px-6 py-2 text-sm",
        lg: "px-8 py-3 text-base",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
)

const Button = React.forwardRef(({ 
  className, 
  variant, 
  size, 
  asChild = false, 
  ...props 
}, ref) => {
  const Comp = asChild ? Slot : "button"
  return (
    <Comp
      className={cn(buttonVariants({ variant, size, className }))}
      ref={ref}
      {...props}
    />
  )
})
Button.displayName = "Button"

export { Button, buttonVariants }
