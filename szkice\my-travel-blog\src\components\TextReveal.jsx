'use client';

import React, { useEffect, useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';

const TextReveal = ({ 
  children, 
  className = '', 
  delay = 0, 
  duration = 0.8,
  staggerDelay = 0.03,
  animationType = 'fadeUp' // 'fadeUp', 'slideIn', 'typewriter', 'awakening'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => setIsVisible(true), delay * 1000);
      return () => clearTimeout(timer);
    }
  }, [isInView, delay]);

  // Split text into words and characters
  const processText = (text) => {
    if (typeof text !== 'string') return text;
    
    return text.split(' ').map((word, wordIndex) => (
      <span key={wordIndex} className="inline-block">
        {word.split('').map((char, charIndex) => (
          <motion.span
            key={`${wordIndex}-${charIndex}`}
            className="inline-block"
            initial={getInitialState()}
            animate={isVisible ? getAnimateState() : getInitialState()}
            transition={{
              duration: duration,
              delay: (wordIndex * 0.1) + (charIndex * staggerDelay),
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
          >
            {char}
          </motion.span>
        ))}
        {wordIndex < text.split(' ').length - 1 && (
          <span className="inline-block">&nbsp;</span>
        )}
      </span>
    ));
  };

  const getInitialState = () => {
    switch (animationType) {
      case 'fadeUp':
        return { opacity: 0, y: 30 };
      case 'slideIn':
        return { opacity: 0, x: -20 };
      case 'typewriter':
        return { opacity: 0, scaleY: 0 };
      case 'awakening':
        return { 
          opacity: 0, 
          y: 20, 
          scale: 0.8,
          filter: 'blur(4px)'
        };
      default:
        return { opacity: 0, y: 30 };
    }
  };

  const getAnimateState = () => {
    switch (animationType) {
      case 'fadeUp':
        return { opacity: 1, y: 0 };
      case 'slideIn':
        return { opacity: 1, x: 0 };
      case 'typewriter':
        return { opacity: 1, scaleY: 1 };
      case 'awakening':
        return { 
          opacity: 1, 
          y: 0, 
          scale: 1,
          filter: 'blur(0px)'
        };
      default:
        return { opacity: 1, y: 0 };
    }
  };

  // Handle different content types
  const renderContent = () => {
    if (typeof children === 'string') {
      return processText(children);
    }

    // For React elements, wrap in motion.div
    return (
      <motion.div
        initial={getInitialState()}
        animate={isVisible ? getAnimateState() : getInitialState()}
        transition={{
          duration: duration,
          delay: delay,
          ease: [0.25, 0.46, 0.45, 0.94],
        }}
      >
        {children}
      </motion.div>
    );
  };

  return (
    <div ref={ref} className={className}>
      {renderContent()}
    </div>
  );
};

// Specialized components for different reveal types
export const AwakeningText = ({ children, ...props }) => (
  <TextReveal animationType="awakening" {...props}>
    {children}
  </TextReveal>
);

export const TypewriterText = ({ children, ...props }) => (
  <TextReveal animationType="typewriter" staggerDelay={0.05} {...props}>
    {children}
  </TextReveal>
);

export const SlideInText = ({ children, ...props }) => (
  <TextReveal animationType="slideIn" {...props}>
    {children}
  </TextReveal>
);

// Haiku-style quote component for testimonials
export const HaikuQuote = ({ quote, author, location, delay = 0 }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-50px" });

  const lines = quote.split('\n').filter(line => line.trim());

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
      transition={{ duration: 1, delay }}
      className="text-center space-y-4 p-8 bg-white/30 backdrop-blur-sm rounded-2xl border border-temple/10"
    >
      <div className="space-y-2">
        {lines.map((line, index) => (
          <AwakeningText
            key={index}
            delay={delay + (index * 0.3)}
            className="block text-lg md:text-xl font-serif font-light text-temple/90 italic"
          >
            {line}
          </AwakeningText>
        ))}
      </div>
      
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
        transition={{ duration: 0.6, delay: delay + (lines.length * 0.3) + 0.5 }}
        className="pt-4 border-t border-temple/20"
      >
        <p className="text-temple/70 font-medium">{author}</p>
        <p className="text-temple/50 text-sm uppercase tracking-wider">{location}</p>
      </motion.div>
    </motion.div>
  );
};

export default TextReveal;
