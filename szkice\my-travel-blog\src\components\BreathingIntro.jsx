'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const BreathingIntro = ({ onComplete, enableSound = false }) => {
  const [currentCycle, setCurrentCycle] = useState(0);
  const [phase, setPhase] = useState('inhale'); // 'inhale', 'exhale'
  const [isVisible, setIsVisible] = useState(true);
  const [showSkip, setShowSkip] = useState(false);
  const [audioContext, setAudioContext] = useState(null);

  const totalCycles = 3;
  const breathDuration = 4000; // 4 seconds

  useEffect(() => {
    // Show skip button after 2 seconds
    const skipTimer = setTimeout(() => setShowSkip(true), 2000);
    
    // Initialize audio context for Tibetan bowl sound
    if (enableSound && typeof window !== 'undefined') {
      try {
        const ctx = new (window.AudioContext || window.webkitAudioContext)();
        setAudioContext(ctx);
      } catch (error) {
        console.log('Audio not supported');
      }
    }

    return () => clearTimeout(skipTimer);
  }, [enableSound]);

  useEffect(() => {
    if (currentCycle >= totalCycles) {
      // Complete the intro after a brief pause
      setTimeout(() => {
        setIsVisible(false);
        setTimeout(onComplete, 800);
      }, 1000);
      return;
    }

    const cycleTimer = setInterval(() => {
      setPhase(prev => {
        if (prev === 'inhale') {
          return 'exhale';
        } else {
          setCurrentCycle(c => c + 1);
          return 'inhale';
        }
      });
    }, breathDuration);

    return () => clearInterval(cycleTimer);
  }, [currentCycle, onComplete]);

  // Generate Tibetan bowl sound
  const playBowlSound = () => {
    if (!audioContext) return;
    
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(60, audioContext.currentTime); // 60Hz
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 3);
    
    oscillator.start();
    oscillator.stop(audioContext.currentTime + 3);
  };

  useEffect(() => {
    if (enableSound && phase === 'inhale' && currentCycle > 0) {
      playBowlSound();
    }
  }, [phase, currentCycle, enableSound, audioContext]);

  const handleSkip = () => {
    setIsVisible(false);
    setTimeout(onComplete, 300);
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.8 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-dawn-mist via-first-ray to-meditation-sky"
      >
        {/* Breathing Circle */}
        <div className="relative flex items-center justify-center">
          <motion.div
            animate={{
              scale: phase === 'inhale' ? 1.5 : 1,
              opacity: phase === 'inhale' ? 1 : 0.3,
            }}
            transition={{
              duration: breathDuration / 1000,
              ease: "easeInOut"
            }}
            className="w-32 h-32 md:w-48 md:h-48 rounded-full bg-gradient-to-br from-temple/20 via-golden/30 to-lotus/40 backdrop-blur-sm border border-temple/10"
          />
          
          {/* Inner breathing guide */}
          <motion.div
            animate={{
              scale: phase === 'inhale' ? 1.2 : 0.8,
              opacity: phase === 'inhale' ? 0.8 : 0.4,
            }}
            transition={{
              duration: breathDuration / 1000,
              ease: "easeInOut"
            }}
            className="absolute w-16 h-16 md:w-24 md:h-24 rounded-full bg-temple/20 backdrop-blur-md"
          />

          {/* Breathing instruction */}
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="absolute -bottom-16 md:-bottom-20 text-center"
          >
            <p className="text-temple/80 font-serif text-lg md:text-xl font-light tracking-wide">
              {phase === 'inhale' ? 'Wdech...' : 'Wydech...'}
            </p>
            <p className="text-temple/60 text-sm mt-2">
              {currentCycle + 1} z {totalCycles}
            </p>
          </motion.div>
        </div>

        {/* Ambient particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              animate={{
                y: [0, -20, 0],
                opacity: [0.2, 0.6, 0.2],
                scale: [0.8, 1.2, 0.8],
              }}
              transition={{
                duration: 6 + i * 0.5,
                repeat: Infinity,
                delay: i * 0.3,
              }}
              className="absolute w-1 h-1 bg-temple/30 rounded-full"
              style={{
                left: `${10 + (i * 7)}%`,
                top: `${20 + (i % 3) * 20}%`,
              }}
            />
          ))}
        </div>

        {/* Skip button */}
        <AnimatePresence>
          {showSkip && (
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0 }}
              onClick={handleSkip}
              className="absolute bottom-8 right-8 px-6 py-3 text-temple/70 hover:text-temple transition-colors duration-300 font-light text-sm tracking-wide border border-temple/20 hover:border-temple/40 rounded-full backdrop-blur-sm bg-white/30 hover:bg-white/50"
            >
              Pomiń wprowadzenie
            </motion.button>
          )}
        </AnimatePresence>

        {/* Sound toggle */}
        {typeof window !== 'undefined' && (
          <motion.button
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            onClick={() => {
              // This would toggle sound in a real implementation
              console.log('Sound toggle clicked');
            }}
            className="absolute bottom-8 left-8 p-3 text-temple/50 hover:text-temple/80 transition-colors duration-300"
            aria-label="Toggle ambient sound"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </motion.button>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export default BreathingIntro;
