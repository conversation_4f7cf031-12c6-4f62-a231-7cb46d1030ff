'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

const ChakraParallax = ({ children }) => {
  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // 7 chakra layers with different parallax speeds
  const chakraLayers = [
    {
      name: '<PERSON><PERSON><PERSON>', // Root
      color: '#C53030', // Red
      speed: 0.1,
      opacity: 0.05,
      size: 120,
      position: { x: '10%', y: '80%' }
    },
    {
      name: '<PERSON><PERSON>dhi<PERSON><PERSON>', // Sacral
      color: '#DD6B20', // Orange
      speed: 0.2,
      opacity: 0.06,
      size: 100,
      position: { x: '85%', y: '70%' }
    },
    {
      name: 'Manipura', // Solar Plexus
      color: '#D69E2E', // Yellow
      speed: 0.3,
      opacity: 0.07,
      size: 110,
      position: { x: '20%', y: '50%' }
    },
    {
      name: '<PERSON><PERSON><PERSON>', // Heart
      color: '#38A169', // Green
      speed: 0.4,
      opacity: 0.08,
      size: 130,
      position: { x: '75%', y: '40%' }
    },
    {
      name: 'Vishuddha', // Throat
      color: '#3182CE', // Blue
      speed: 0.5,
      opacity: 0.06,
      size: 90,
      position: { x: '15%', y: '25%' }
    },
    {
      name: 'Ajna', // Third Eye
      color: '#553C9A', // Indigo
      speed: 0.6,
      opacity: 0.07,
      size: 80,
      position: { x: '80%', y: '15%' }
    },
    {
      name: 'Sahasrara', // Crown
      color: '#805AD5', // Violet
      speed: 0.7,
      opacity: 0.05,
      size: 140,
      position: { x: '50%', y: '5%' }
    }
  ];

  return (
    <div ref={containerRef} className="relative">
      {/* Chakra parallax layers */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        {chakraLayers.map((chakra, index) => {
          const y = useTransform(scrollYProgress, [0, 1], [0, -window.innerHeight * chakra.speed]);
          const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [chakra.opacity, chakra.opacity * 1.5, 0]);
          const scale = useTransform(scrollYProgress, [0, 0.5, 1], [1, 1.2, 0.8]);
          const rotate = useTransform(scrollYProgress, [0, 1], [0, 360 * (chakra.speed * 0.5)]);

          return (
            <motion.div
              key={chakra.name}
              style={{
                y,
                opacity,
                scale,
                rotate,
                left: chakra.position.x,
                top: chakra.position.y,
              }}
              className="absolute"
            >
              {/* Chakra symbol */}
              <div
                className="rounded-full blur-sm"
                style={{
                  width: chakra.size,
                  height: chakra.size,
                  background: `radial-gradient(circle, ${chakra.color}40 0%, ${chakra.color}20 50%, transparent 100%)`,
                }}
              />
              
              {/* Inner mandala pattern */}
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20 + index * 5, repeat: Infinity, ease: "linear" }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <svg
                  width={chakra.size * 0.6}
                  height={chakra.size * 0.6}
                  viewBox="0 0 100 100"
                  className="opacity-30"
                >
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill="none"
                    stroke={chakra.color}
                    strokeWidth="1"
                    strokeDasharray="5,5"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="25"
                    fill="none"
                    stroke={chakra.color}
                    strokeWidth="0.5"
                    strokeDasharray="3,3"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="10"
                    fill={chakra.color}
                    opacity="0.3"
                  />
                </svg>
              </motion.div>
            </motion.div>
          );
        })}

        {/* Energy flow particles */}
        <div className="absolute inset-0">
          {[...Array(30)].map((_, i) => {
            const particleY = useTransform(scrollYProgress, [0, 1], [0, -window.innerHeight * (0.3 + i * 0.02)]);
            const particleOpacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 0.6, 0.8, 0]);
            
            return (
              <motion.div
                key={i}
                style={{
                  y: particleY,
                  opacity: particleOpacity,
                }}
                className="absolute w-1 h-1 bg-temple/20 rounded-full"
                style={{
                  left: `${10 + (i * 2.8)}%`,
                  top: `${20 + (i % 7) * 10}%`,
                }}
              />
            );
          })}
        </div>

        {/* Ambient light rays */}
        <motion.div
          style={{
            opacity: useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 0.3, 0.5, 0]),
            scale: useTransform(scrollYProgress, [0, 1], [1, 1.5]),
          }}
          className="absolute inset-0 bg-gradient-radial from-golden/5 via-transparent to-transparent"
        />
      </div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default ChakraParallax;
