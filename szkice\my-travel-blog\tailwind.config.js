/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './src/app/**/*.{js,jsx,ts,tsx}',
    './src/components/**/*.{js,jsx,ts,tsx}',
    './src/pages/**/*.{js,jsx,ts,tsx}',
  ],
  corePlugins: {
    preflight: true,
  },
  theme: {
    extend: {
      colors: {
        // Elegancka, minimalistyczna paleta - tylko 5 głównych kolorów
        primary: '#1A1A1A',        // Głęboka czerń dla tekstu
        secondary: '#F8F6F3',      // Kremowa biel dla tła
        accent: '#8B7355',         // Ciepły brąz (między temple a wood)
        'soft-sage': '#E8E5E0',    // <PERSON><PERSON><PERSON> delikatny beż
        'warm-gold': '#D4A574',    // Złoty dla akcentów

        // Dodatkowe kolory używane w CSS
        temple: '#8B7355',         // Główny kolor brązowy
        earth: '#A0956B',          // Ziemisty brąz
        golden: '#D4A574',         // Złoty
        wood: '#6B5B47',           // Ciemny brąz
        'wood-light': '#8B7355',   // Jasny brąz
        bamboo: '#9CAF88',         // Zielony bambusowy
        lotus: '#E8D5C4',          // Kremowy lotus
        shell: '#F5F2ED',          // Skorupkowy
        sage: '#A8B5A0',           // Szałwiowy
        rice: '#F8F6F3',           // Ryżowy (jak secondary)
        ocean: '#7A9CC6',          // Oceaniczny niebieski
      },
      fontFamily: {
        sans: ['var(--font-montserrat)', 'system-ui', 'sans-serif'],
        serif: ['var(--font-cormorant)', 'Georgia', 'serif'],
        display: ['var(--font-cormorant)', 'Georgia', 'serif'],
      },
      fontSize: {
        sm: ['0.875rem', { lineHeight: '1.6rem', letterSpacing: '0.1em' }],
        base: ['1rem', { lineHeight: '1.8rem' }],
        lg: ['1.125rem', { lineHeight: '2rem' }],
        xl: ['1.25rem', { lineHeight: '2.1rem', letterSpacing: '-0.01em' }],
        '2xl': ['1.5rem', { lineHeight: '2.3rem', letterSpacing: '-0.01em' }],
        '3xl': ['1.875rem', { lineHeight: '2.6rem', letterSpacing: '-0.01em' }],
        '4xl': ['2.25rem', { lineHeight: '2.8rem', letterSpacing: '-0.02em' }],
        '5xl': ['3rem', { lineHeight: '3.6rem', letterSpacing: '-0.02em' }],
      },
      spacing: {
        xs: '0.5rem',
        sm: '1rem',
        md: '1.5rem',
        lg: '2rem',
        xl: '3rem',
        '2xl': '4rem',
        '3xl': '6rem',    // Dodatkowa przestrzeń dla większej elegancji
        '4xl': '8rem',    // Jeszcze większa przestrzeń
      },
      height: {
        hero: '100vh',
        'hero-sm': '85vh',
      },
      maxWidth: {
        content: '80rem',
        '6xl': '72rem',
      },
      animation: {
        'fade-in': 'fadeIn 1.2s ease-out forwards',
        'slide-up': 'slideUp 1s ease-out forwards',
        parallax: 'parallax 15s linear infinite',
        'float-gentle': 'floatGentle 6s ease-in-out infinite',
        'scale-in': 'scaleIn 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards',
        'hover-lift': 'hoverLift 0.3s ease-out forwards',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(15px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        parallax: {
          '0%': { backgroundPosition: '0% 0%' },
          '100%': { backgroundPosition: '0% 100%' },
        },
        floatGentle: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-4px)' },
        },
        scaleIn: {
          'from': { opacity: '0', transform: 'scale(0.95)' },
          'to': { opacity: '1', transform: 'scale(1)' },
        },
        hoverLift: {
          'to': { transform: 'translateY(-2px)' },
        },
      },
      transitionTimingFunction: {
        gentle: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'ease-gentle': 'cubic-bezier(0.23, 1, 0.32, 1)',
        'ease-smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
        'ease-bounce': 'cubic-bezier(0.34, 1.56, 0.64, 1)',
        'ease-out-smooth': 'cubic-bezier(0.16, 1, 0.3, 1)',
      },
      transitionDuration: {
        gentle: '400ms',
        smooth: '300ms',
        soft: '200ms',
      },
      boxShadow: {
        soft: 'var(--shadow-soft)',
        medium: 'var(--shadow-medium)',
        'medium-warm': 'var(--shadow-medium-warm)',
        glow: 'var(--shadow-glow)',
        warm: 'var(--shadow-warm)',
        subtle: 'var(--shadow-subtle)',
        'glass': 'var(--shadow-glass)',
      },
      borderRadius: {
        xl: '0.75rem', 
        lg: '0.5rem',
        md: '0.375rem',
        sm: '0.25rem',
      },
      backgroundImage: {
        'bali-texture': "url('/images/bali-texture.webp')",
        'gradient-hero': 'linear-gradient(135deg, rgba(var(--color-temple), 0.4), rgba(var(--color-ocean), 0.2))',
        'shell-gradient': 'var(--gradient-shell)',
        'warm-gradient': 'var(--gradient-warm)',
        'ocean-gradient': 'var(--gradient-ocean)',
        'sunset-gradient': 'var(--gradient-sunset)',
        'gentle-gradient': 'var(--gradient-gentle)',
      },
      backdropBlur: {
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
      },
    },
  },
  plugins: [],
};