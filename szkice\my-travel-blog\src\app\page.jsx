import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import ParallaxHero from '@/components/ParallaxHero';
import TrustBadges from '@/components/TrustBadges';
import NewsletterSignup from '@/components/NewsletterSignup';
import { ScrollReveal, StaggerContainer, StaggerItem, HoverCard } from '@/components/ScrollReveal';

// Fallback mock data for testing
const mockBlogPosts = [
  {
    slug: 'stanie-na-rekach-odkryj-swoja-wewnetrzna-sile-i-odwage',
    title: 'Stanie na Rękach: Odkryj Swoją Wewnętrzną Siłę i Odwagę',
    excerpt: 'Przekrocz granice strachu i odkryj niesamowitą moc swojego ciała.',
    imageUrl: '/images/blog/handstand-828.webp',
  },
  {
    slug: 'szpagaty-otworz-biodra-i-uwolnij-swoja-kobiecosc',
    title: 'Szpagaty: Otwórz Biodra i Uwolnij Swoją Kobie<PERSON>ś<PERSON>',
    excerpt: 'Odkryj sekret elastyczności i gracji.',
    imageUrl: '/images/blog/temple-bali.webp',
  },
  {
    slug: 'kobieca-sila-w-jodze-odkryj-swoja-wewnetrzna-boginie',
    title: 'Kobieca Siła w Jodze: Odkryj Swoją Wewnętrzną Boginię',
    excerpt: 'Poznaj unikalne aspekty praktyki jogi dla kobiet.',
    imageUrl: '/images/blog/temple-bali.webp',
  }
];

// Simple icon placeholders to avoid import issues
const IconPlaceholder = ({ className = 'w-5 h-5' }) => (
  <div className={`${className} bg-temple/20 rounded`} />
);

// SSR-friendly SafeIcon component - using placeholder for now
const SafeIcon = ({ className = 'w-5 h-5' }) => {
  return <IconPlaceholder className={className} />;
};

const HeroSection = () => {
  return (
    <ParallaxHero
      imageUrl="/images/background/bali-hero.webp"
      parallaxSpeed={0.3}
    >
      <div className="space-y-16">
        <div className="subtitle mb-8 text-xs uppercase tracking-[0.3em] opacity-60">Retreaty Jogowe</div>

        <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-light mb-12 tracking-tight leading-[0.95]">
          Bali Yoga<br />Journey
        </h1>

        <div className="space-y-8">
          <p className="text-lg max-w-md mx-auto leading-relaxed font-light opacity-80">
            Harmonia ducha i tropikalnej przygody
          </p>

          {/* Subtelny separator */}
          <div className="flex items-center justify-center">
            <div className="h-px w-16 bg-gradient-to-r from-transparent via-current to-transparent opacity-30" />
          </div>

          <Link
            href="#journey-inspiration"
            className="group inline-block text-sm uppercase tracking-[0.2em] font-light relative pb-1 border-b border-transparent hover:border-current transition-all duration-500"
            aria-label="Odkryj podróż jogową na Bali"
          >
            Odkryj Podróż
            <div className="absolute bottom-0 left-0 w-0 h-px bg-current transition-all duration-500 group-hover:w-full"></div>
          </Link>
        </div>

        {/* Mobile swipe hint */}
        <div className="swipe-hint md:hidden">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </ParallaxHero>
  );
};

const Card = ({ type = 'post', title, description, link, imageUrl, index = 0 }) => {
  const isHighlight = type === 'highlight';
  const isTestimonial = type === 'testimonial';

  // Asymetryczne wysokości dla dynamiki
  const getCardHeight = () => {
    if (isHighlight) return index % 2 === 0 ? 'min-h-[320px]' : 'min-h-[280px]';
    if (isTestimonial) return index % 3 === 0 ? 'min-h-[300px]' : 'min-h-[260px]';
    return 'min-h-[400px]';
  };

  // Asymetryczne przesunięcia
  const getAsymmetricOffset = () => {
    if (index % 3 === 0) return 'mt-8';
    if (index % 3 === 1) return 'mt-0';
    return 'mt-4';
  };

  if (isHighlight) {
    return (
      <HoverCard className={`group bg-white/60 backdrop-blur-sm shadow-soft ${getCardHeight()} ${getAsymmetricOffset()}`}>
        <div className="p-8 flex flex-col flex-grow h-full">
          <h3 className="text-2xl font-serif font-light mb-8 tracking-wide group-hover:text-warm-gold transition-colors duration-300">{title}</h3>
          <p className="font-light text-lg leading-loose flex-grow opacity-80">{description}</p>
        </div>
      </HoverCard>
    );
  }

  if (isTestimonial) {
    return (
      <div className={`group bg-white/40 backdrop-blur-sm shadow-soft hover:shadow-medium transition-all duration-500 hover:-translate-y-1 ${getCardHeight()} ${getAsymmetricOffset()}`}>
        <div className="p-8 flex flex-col flex-grow h-full relative">
          {/* Subtelny cudzysłów w tle */}
          <span className="absolute -top-2 -left-1 text-6xl text-warm-gold/5 font-serif leading-none select-none">"</span>

          <blockquote className="relative">
            <p className="leading-loose mb-12 italic text-lg opacity-90">"{description}"</p>
            <footer className="flex items-center mt-auto">
              <div className="w-16 h-16 rounded-full bg-warm-gold/10 flex items-center justify-center font-serif text-xl group-hover:bg-warm-gold/20 transition-colors duration-300">
                {title.charAt(0)}
              </div>
              <div className="ml-6">
                <cite className="block font-medium text-lg not-italic">{title}</cite>
                <p className="text-sm opacity-60 uppercase tracking-wider">{link}</p>
              </div>
            </footer>
          </blockquote>
        </div>
      </div>
    );
  }

  return (
    <div className={`group ${getCardHeight()} ${getAsymmetricOffset()}`}>
      <div className="bg-white/50 backdrop-blur-sm shadow-soft hover:shadow-medium transition-all duration-500 hover:-translate-y-3 h-full flex flex-col">
        <div className="relative h-[300px] overflow-hidden">
          <Image
            src={imageUrl || '/images/placeholder/image.jpg'}
            alt={title}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            loading="lazy"
          />
        </div>
        <div className="p-8 flex flex-col flex-grow">
          <h3 className="text-2xl font-serif mb-6 font-light tracking-wide">
            <Link href={link || '#'} className="hover:text-warm-gold transition-colors duration-300">
              {title}
            </Link>
          </h3>
          <p className="text-lg leading-loose mb-8 flex-grow font-light opacity-80">{description}</p>
          <Link
            href={link || '#'}
            className="group inline-flex items-center gap-2 text-sm font-light mt-auto self-start uppercase tracking-wider text-warm-gold hover:gap-3 transition-all duration-300"
          >
            <span>Czytaj dalej</span>
            <span className="transform group-hover:translate-x-1 transition-transform">→</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

// Static data - no need for useMemo in Server Component
const homepagePosts = mockBlogPosts;

const eventData = {
  highlights: [
    {
      id: 'ubud',
      title: 'Ubud, Bali',
      description: 'Duchowe serce Bali, zanurzenie w kulturze i jodze wśród tarasów ryżowych i świętych świątyń.',
      destination: 'bali'
    },
    {
      id: 'sigiriya',
      title: 'Sigiriya, Sri Lanka',
      description: 'Lwia Skała - starożytna forteca na wysokości 200m, idealna na medytację i praktykę mindfulness.',
      destination: 'srilanka'
    },
    {
      id: 'gili-air',
      title: 'Gili Air, Bali',
      description: 'Rajska wyspa bez samochodów, idealna na relaks, snorkeling i medytację nad oceanem.',
      destination: 'bali'
    },
    {
      id: 'ella',
      title: 'Ella, Sri Lanka',
      description: 'Górski raj z plantacjami herbaty, idealny na retreaty jogowe z widokiem na nieskończone wzgórza.',
      destination: 'srilanka'
    },
    {
      id: 'uluwatu',
      title: 'Uluwatu, Bali',
      description: 'Joga na klifach z widokiem na bezkresny ocean i spektakularne zachody słońca.',
      destination: 'bali'
    },
  ],
};

const combinedItems = eventData.highlights.map((item) => ({ ...item, type: 'highlight' }));

const socialLinks = [
  {
    id: 'instagram',
    href: 'https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr',
    label: 'Instagram',
    aria: 'Profil na Instagramie',
  },
  {
    id: 'facebook',
    href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/',
    label: 'Facebook',
    aria: 'Profil na Facebooku',
  },
  {
    id: 'fitssey',
    href: 'https://app.fitssey.com/Flywithbakasana/frontoffice',
    label: 'Fitssey',
    aria: 'Profil na Fitssey (rezerwacje)',
  },
];

export default function HomePage() {

  return (
    <div className="relative">
      <HeroSection />

      {/* Trust Badges - dodane zaraz po hero */}
      <TrustBadges />

      <section id="journey-inspiration" className="section py-20 md:py-24 lg:py-32 relative">
        <div className="section-number">01</div>
        <div className="max-w-7xl mx-auto px-8 sm:px-12 lg:px-16 relative z-10">
          <ScrollReveal variant="fadeInUp" className="text-center mb-24">
            <div className="subtitle mb-8 text-xs uppercase tracking-[0.3em] opacity-60">Nasze Miejsca</div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-serif font-light mb-8 tracking-tight">Podróż i Inspiracje</h2>
            <div className="section-content">
              <p className="font-light text-xl max-w-2xl mx-auto leading-loose opacity-80">Odkryj magiczne miejsca Bali i Sri Lanki</p>
            </div>
          </ScrollReveal>
          <div className="space-y-24">
            {/* Enhanced grid with destination variety */}
            <StaggerContainer className="grid gap-12 lg:gap-16">
              <div className="grid gap-12 md:grid-cols-2 lg:grid-cols-2">
                {combinedItems.slice(0, 2).map((item, index) => (
                  <StaggerItem key={item.id}>
                    <Card
                      type={item.type}
                      title={item.title}
                      description={item.description}
                      icon={item.icon}
                      index={index}
                    />
                  </StaggerItem>
                ))}
              </div>
              <div className="grid gap-12 md:grid-cols-3 lg:grid-cols-3">
                {combinedItems.slice(2, 5).map((item, index) => (
                  <StaggerItem key={item.id}>
                    <Card
                      type={item.type}
                      title={item.title}
                      description={item.description}
                      icon={item.icon}
                      index={index + 2}
                    />
                  </StaggerItem>
                ))}
              </div>
            </StaggerContainer>
            <div className="text-center pt-16">
              <Link
                href="/program"
                className="group inline-block text-sm uppercase tracking-[0.2em] font-light relative pb-1 border-b border-transparent hover:border-current transition-all duration-500"
                aria-label="Poznaj szczegóły programu"
              >
                Poznaj szczegóły programu
                <div className="absolute bottom-0 left-0 w-0 h-px bg-current transition-all duration-500 group-hover:w-full"></div>
              </Link>
            </div>
          </div>
        </div>
      </section>
      <section className="section lotus-divider py-20 md:py-24 lg:py-32 bg-soft-sage/20 relative">
        <div className="section-number">02</div>
        <div className="max-w-7xl mx-auto px-8 sm:px-12 lg:px-16 relative z-10">
          <ScrollReveal variant="fadeInUp" className="text-center mb-24">
            <div className="subtitle mb-8 text-xs uppercase tracking-[0.3em] opacity-60">Opinie</div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-serif font-light mb-8 tracking-tight">
              Co <span className="text-outline">mówią</span> uczestnicy
            </h2>
            <div className="section-content">
              <p className="font-light text-xl max-w-2xl mx-auto leading-loose opacity-80">Poznaj opinie osób, które doświadczyły naszych retreatów</p>
            </div>
          </ScrollReveal>
          <div className="space-y-24">
            {/* Asymetryczny layout 1-2 */}
            <div className="grid gap-12 lg:gap-16">
              <div className="grid gap-12 md:grid-cols-1 lg:grid-cols-1 max-w-2xl">
                {[
                  {
                    id: 'marta-k',
                    name: 'Marta K.',
                    text: 'Retreat z Julią to najlepsze, co mogłam dla siebie zrobić. Połączenie jogi, medytacji i eksploracji Bali było idealnie wyważone. Wróciłam odmieniona!',
                    location: 'Warszawa',
                  }
                ].map((testimonial, index) => (
                  <Card
                    key={testimonial.id}
                    type="testimonial"
                    title={testimonial.name}
                    description={testimonial.text}
                    link={testimonial.location}
                    index={index}
                  />
                ))}
              </div>
              <div className="grid gap-12 md:grid-cols-2 lg:grid-cols-2 ml-auto max-w-4xl">
                {[
                  {
                    id: 'tomasz-w',
                    name: 'Tomasz W.',
                    text: 'Jako początkujący w jodze obawiałem się, czy dam radę. Julia stworzyła przestrzeń, w której każdy mógł praktykować na swoim poziomie. Bali zachwyciło mnie!',
                    location: 'Kraków',
                  },
                  {
                    id: 'karolina-m',
                    name: 'Karolina M.',
                    text: 'Trzeci raz uczestniczę w retreatach Julii i za każdym razem odkrywam coś nowego - zarówno w praktyce jogi, jak i w sobie. Polecam z całego serca!',
                    location: 'Wrocław',
                  },
                ].map((testimonial, index) => (
                  <Card
                    key={testimonial.id}
                    type="testimonial"
                    title={testimonial.name}
                    description={testimonial.text}
                    link={testimonial.location}
                    index={index + 1}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="py-20 md:py-24 lg:py-32 relative">
        <div className="section-number">03</div>
        <div className="max-w-7xl mx-auto px-8 sm:px-12 lg:px-16 relative z-10">
          {/* Pionowy layout z vertical text */}
          <div className="flex flex-col lg:flex-row gap-24 lg:gap-32 mb-24">
            <ScrollReveal variant="fadeInLeft" className="lg:w-1/3">
              <div className="subtitle mb-8 text-xs uppercase tracking-[0.3em] opacity-60">Blog</div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-serif font-light tracking-tight leading-tight">
                Z naszego<br />
                <span className="text-golden">bloga</span>
              </h2>
            </ScrollReveal>
            <ScrollReveal variant="fadeInRight" delay={0.2} className="lg:w-2/3 flex items-end">
              <p className="font-light text-xl leading-loose opacity-80 max-w-xl">
                Przeczytaj najnowsze artykuły o jodze, Bali i podróżach
              </p>
            </ScrollReveal>
          </div>

          {/* Divider line */}
          <div className="divider-line"></div>

          {/* Nierówny grid - elementy wychodzące poza siatkę */}
          <div className="space-y-24">
            <div className="grid gap-12 lg:gap-16">
              {/* Pierwszy post - pełna szerokość, przesunięty */}
              <div className="lg:-ml-16 lg:mr-8">
                <Card
                  key={homepagePosts[0].slug}
                  type="post"
                  title={homepagePosts[0].title}
                  description={homepagePosts[0].excerpt}
                  link={`/blog/${homepagePosts[0].slug}`}
                  index={0}
                  imageUrl={homepagePosts[0].imageUrl}
                />
              </div>

              {/* Pozostałe posty - asymetryczny grid */}
              <div className="grid gap-12 md:grid-cols-2 lg:grid-cols-2 lg:ml-8 lg:-mr-16">
                {homepagePosts.slice(1).map((post, index) => (
                  <Card
                    key={post.slug}
                    type="post"
                    title={post.title}
                    description={post.excerpt}
                    link={`/blog/${post.slug}`}
                    index={index + 1}
                    imageUrl={post.imageUrl}
                  />
                ))}
              </div>
            </div>

            <div className="text-center pt-16">
              <Link
                href="/blog"
                className="group inline-block text-sm uppercase tracking-[0.2em] font-light relative pb-1 border-b border-transparent hover:border-current transition-all duration-500"
                aria-label="Zobacz wszystkie artykuły"
              >
                Zobacz wszystkie artykuły
                <div className="absolute bottom-0 left-0 w-0 h-px bg-current transition-all duration-500 group-hover:w-full"></div>
              </Link>
            </div>
          </div>
        </div>
      </section>
      <section className="py-20 md:py-24 lg:py-32 bg-soft-sage/10">
        <div className="max-w-7xl mx-auto px-8 sm:px-12 lg:px-16">
          <div className="text-center mb-24">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-serif font-light tracking-tight mb-8">
              Połączmy <span className="text-warm-gold">się</span>
            </h2>
            <p className="font-light text-xl max-w-2xl mx-auto leading-loose opacity-80">
              Śledź nas w mediach społecznościowych
            </p>
          </div>

          {/* Minimalistyczny grid social media */}
          <div className="grid gap-8 md:grid-cols-3 max-w-4xl mx-auto">
            {socialLinks.map((link, index) => (
              <a
                key={link.id}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={link.aria}
                className="bg-white/30 backdrop-blur-sm shadow-soft hover:shadow-medium hover:-translate-y-2 transition-all duration-500 p-12 text-center group"
              >
                <div className="mb-6">
                  <SafeIcon className="w-8 h-8 text-temple mx-auto opacity-60 group-hover:opacity-100 transition-opacity" />
                </div>
                <span className="text-temple font-light text-lg tracking-wide">{link.label}</span>
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-20 md:py-24 lg:py-32 bg-gradient-to-b from-shell/10 to-temple/5">
        <div className="max-w-7xl mx-auto px-8 sm:px-12 lg:px-16">
          <NewsletterSignup variant="inline" className="mb-16" />
        </div>
      </section>

      <section className="py-20 md:py-24 lg:py-32 relative">
        <div className="max-w-7xl mx-auto px-8 sm:px-12 lg:px-16">
          {/* Ogromny cytat jako element designu */}
          <div className="text-center mb-24">
            <blockquote className="text-2xl md:text-3xl lg:text-4xl font-serif font-light italic text-temple max-w-4xl mx-auto mb-8 leading-tight">
              Gotowa na przygodę życia?
            </blockquote>

            {/* Dodaj subtelny separator */}
            <div className="flex items-center justify-center mb-16">
              <div className="h-px w-24 bg-gradient-to-r from-transparent via-warm-gold to-transparent opacity-50" />
            </div>

            <p className="font-light text-xl max-w-2xl mx-auto leading-loose opacity-80 mb-16">
              Dołącz do naszego najbliższego retreatu jogowego na Bali i odkryj harmonię ciała i ducha
            </p>
            <div className="meta-text mb-12">
              Bali • 2025 • Transformacja
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-8 justify-center max-w-2xl mx-auto">
            <Link
              href="/kontakt"
              className="group inline-block text-sm uppercase tracking-[0.2em] font-light relative pb-1 border-b border-transparent hover:border-warm-gold transition-all duration-500 text-warm-gold"
              aria-label="Skontaktuj się z nami"
            >
              Skontaktuj się z nami
              <div className="absolute bottom-0 left-0 w-0 h-px bg-warm-gold transition-all duration-500 group-hover:w-full"></div>
            </Link>
            <Link
              href="/program"
              className="group inline-block text-sm uppercase tracking-[0.2em] font-light relative pb-1 border-b border-transparent hover:border-current transition-all duration-500"
              aria-label="Zobacz program"
            >
              Zobacz program
              <div className="absolute bottom-0 left-0 w-0 h-px bg-current transition-all duration-500 group-hover:w-full"></div>
            </Link>
          </div>
        </div>
      </section>

      {/* Enhanced FAQ Section - ukryj 50% na mobile */}
      <section className="py-16 bg-gradient-to-b from-shell/20 to-white mobile-hidden">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif text-temple mb-4">
              Często Zadawane Pytania
            </h2>
            <p className="text-wood-light/80 text-lg mb-8">
              Znajdź odpowiedzi na najczęściej zadawane pytania o nasze retreaty
            </p>
            <div className="w-24 h-px bg-temple/20 mx-auto"></div>
          </div>

          <div className="space-y-6 text-left max-w-3xl mx-auto">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-temple/10 p-6">
              <h3 className="text-lg font-medium text-temple mb-3">Czy muszę znać angielski na Bali?</h3>
              <p className="text-wood-light/80 leading-relaxed">Nie! Julia prowadzi wszystkie zajęcia w języku polskim. W hotelach i restauracjach podstawowy angielski wystarczy, a my jesteśmy zawsze blisko, żeby pomóc. Większość miejsc na Bali jest przystosowana do turystów.</p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-temple/10 p-6">
              <h3 className="text-lg font-medium text-temple mb-3">Czy jest bezpiecznie dla kobiet podróżujących samotnie?</h3>
              <p className="text-wood-light/80 leading-relaxed">Absolutnie tak! Bali to jedna z najbezpieczniejszych destynacji w Azji. Podróżujemy w grupie, mieszkamy w sprawdzonych miejscach, a Julia ma 5+ lat doświadczenia w organizacji wyjazdów. 95% naszych uczestniczek to kobiety.</p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-temple/10 p-6">
              <h3 className="text-lg font-medium text-temple mb-3">Co jest wliczone w cenę retreatu?</h3>
              <p className="text-wood-light/80 leading-relaxed">W cenę wliczone jest: zakwaterowanie (pokoje 2-osobowe), wszystkie posiłki (śniadania, obiady, kolacje), codzienne zajęcia jogi i medytacji, warsztaty tematyczne, wycieczki zgodnie z programem, transport lokalny na Bali oraz opieka instruktorki przez cały pobyt.</p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-temple/10 p-6">
              <h3 className="text-lg font-medium text-temple mb-3">Jaki poziom jogi jest wymagany?</h3>
              <p className="text-wood-light/80 leading-relaxed">Retreat jest dostosowany do wszystkich poziomów! Julia prowadzi zajęcia z modyfikacjami dla początkujących i wyzwaniami dla zaawansowanych. Najważniejsza jest otwartość na nowe doświadczenia. 40% uczestników to osoby początkujące.</p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-temple/10 p-6">
              <h3 className="text-lg font-medium text-temple mb-3">Ile kosztuje wyjazd i co nie jest wliczone?</h3>
              <p className="text-wood-light/80 leading-relaxed">Cena retreatu to 2900-4500 PLN (zależnie od terminu i typu pokoju). Dodatkowo płacisz: lot do Bali (ok. 2500-3500 PLN), wizę (35 USD), ubezpieczenie podróżne oraz wydatki osobiste. Pomagamy w organizacji lotów.</p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-temple/10 p-6">
              <h3 className="text-lg font-medium text-temple mb-3">Jakie dokumenty potrzebuję do wyjazdu?</h3>
              <p className="text-wood-light/80 leading-relaxed">Potrzebujesz: paszport ważny min. 6 miesięcy od daty wyjazdu, wizę turystyczną (można kupić online za 35 USD lub na lotnisku), ubezpieczenie podróżne oraz zaświadczenie o szczepieniach (jeśli wymagane). Pomagamy w przygotowaniu dokumentów.</p>
            </div>
          </div>

          <div className="mt-12 text-center">
            <p className="text-wood-light/70 mb-4">
              Nie znalazłeś odpowiedzi na swoje pytanie?
            </p>
            <a
              href="https://wa.me/48606101523?text=Cześć! Mam pytanie dotyczące retreatu jogi na Bali."
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full transition-all duration-300 hover:scale-105"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
              </svg>
              Napisz na WhatsApp
            </a>
          </div>
        </div>
      </section>

      {/* Internal Links - Simplified - ukryj na mobile */}
      <section className="py-12 mobile-hidden">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-temple/5 to-bamboo/5 rounded-2xl p-8 border border-temple/10">
            <h3 className="text-xl font-serif text-temple mb-6 text-center">
              Może Cię również zainteresować
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link
                href="/program"
                className="group flex items-center gap-3 p-4 bg-shell/60 backdrop-blur-sm rounded-xl border border-temple/10 hover:border-temple/20 hover:shadow-medium transition-all duration-300 hover:-translate-y-1"
              >
                <div className="p-2 bg-temple/10 rounded-full group-hover:bg-temple/20 transition-colors duration-300">
                  <SafeIcon className="w-4 h-4 text-temple" />
                </div>
                <span className="text-temple font-medium text-sm group-hover:text-golden transition-colors duration-300">
                  Program Retreatów
                </span>
                <SafeIcon className="w-4 h-4 text-temple/40 ml-auto group-hover:text-temple group-hover:translate-x-1 transition-all duration-300" />
              </Link>

              <Link
                href="/blog"
                className="group flex items-center gap-3 p-4 bg-shell/60 backdrop-blur-sm rounded-xl border border-temple/10 hover:border-temple/20 hover:shadow-medium transition-all duration-300 hover:-translate-y-1"
              >
                <div className="p-2 bg-temple/10 rounded-full group-hover:bg-temple/20 transition-colors duration-300">
                  <SafeIcon className="w-4 h-4 text-temple" />
                </div>
                <span className="text-temple font-medium text-sm group-hover:text-golden transition-colors duration-300">
                  Blog o Jodze
                </span>
                <SafeIcon className="w-4 h-4 text-temple/40 ml-auto group-hover:text-temple group-hover:translate-x-1 transition-all duration-300" />
              </Link>

              <Link
                href="/kontakt"
                className="group flex items-center gap-3 p-4 bg-shell/60 backdrop-blur-sm rounded-xl border border-temple/10 hover:border-temple/20 hover:shadow-medium transition-all duration-300 hover:-translate-y-1"
              >
                <div className="p-2 bg-temple/10 rounded-full group-hover:bg-temple/20 transition-colors duration-300">
                  <SafeIcon className="w-4 h-4 text-temple" />
                </div>
                <span className="text-temple font-medium text-sm group-hover:text-golden transition-colors duration-300">
                  Skontaktuj się
                </span>
                <SafeIcon className="w-4 h-4 text-temple/40 ml-auto group-hover:text-temple group-hover:translate-x-1 transition-all duration-300" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* WhatsApp Button - floating */}
      <a
        href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
        target="_blank"
        rel="noopener noreferrer"
        className="fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg hover:scale-110 transition-all duration-300 z-50 group"
        aria-label="Skontaktuj się przez WhatsApp"
      >
        <svg
          className="w-6 h-6"
          fill="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
        </svg>

        {/* Tooltip */}
        <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
          Napisz na WhatsApp
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      </a>

      {/* Gesture hint - tylko na mobile */}
      <div className="gesture-hint md:hidden">
        <svg className="w-4 h-4 text-temple" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </div>
  );
}
