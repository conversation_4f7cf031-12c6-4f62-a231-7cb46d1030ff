# 🔧 ENVIRONMENT VARIABLES DLA VERCEL

## 📋 **ZMIENNE DO DODANIA W VERCEL**

Po deploymencie na Vercel, dodaj te zmienne w **Settings → Environment Variables**:

### **🌐 PODSTAWOWE USTAWIENIA DOMENY**
```
NEXT_PUBLIC_SITE_NAME = bakasana-travel.blog
NEXT_PUBLIC_SITE_DESCRIPTION = Odkryj piękno Bali z nami
NEXT_PUBLIC_BASE_URL = https://bakasana-travel.blog
NEXT_PUBLIC_SITE_URL = https://bakasana-travel.blog
NEXT_PUBLIC_SITE_IMAGE_URL = /og-image.jpg
```

### **📊 GOOGLE ANALYTICS**
```
NEXT_PUBLIC_GA_ID = G-M780DCS04D
NEXT_PUBLIC_GA_MEASUREMENT_ID = G-M780DCS04D
```

### **🔍 GOOGLE SEARCH CONSOLE**
```
NEXT_PUBLIC_GOOGLE_VERIFICATION = your-verification-code-here
```
**UWAGA:** Kod wery<PERSON>ka<PERSON>jny dostaniesz po dodaniu domeny w Google Search Console

### **⚙️ INNE USTAWIENIA**
```
NODE_ENV = production
ANALYZE = false
```

---

## 🎯 **INSTRUKCJA DODAWANIA W VERCEL**

1. **Idź do swojego projektu w Vercel**
2. **Kliknij "Settings"**
3. **Wybierz "Environment Variables"**
4. **Dla każdej zmiennej:**
   - Kliknij "Add New"
   - Wpisz **Name** (np. `NEXT_PUBLIC_SITE_URL`)
   - Wpisz **Value** (np. `https://bakasana-travel.blog`)
   - Wybierz **Environment**: Production, Preview, Development (lub wszystkie)
   - Kliknij "Save"

5. **Po dodaniu wszystkich zmiennych:**
   - Idź do "Deployments"
   - Kliknij "..." przy ostatnim deploymencie
   - Wybierz "Redeploy"

---

## 🔍 **GOOGLE SEARCH CONSOLE - JAK UZYSKAĆ KOD WERYFIKACYJNY**

1. **Idź na [search.google.com/search-console](https://search.google.com/search-console)**
2. **Kliknij "Add Property"**
3. **Wybierz "Domain" (nie URL prefix)**
4. **Wpisz `bakasana-travel.blog`**
5. **Google pokaże instrukcje weryfikacji DNS**
6. **Skopiuj kod TXT record i dodaj do DNS u dostawcy domeny**
7. **Kliknij "Verify"**

**Alternatywnie - HTML tag method:**
1. **Wybierz "URL prefix" zamiast "Domain"**
2. **Wpisz `https://bakasana-travel.blog`**
3. **Wybierz "HTML tag" method**
4. **Skopiuj kod** (np. `google1234567890abcdef`)
5. **Dodaj do Vercel jako `NEXT_PUBLIC_GOOGLE_VERIFICATION`**

---

## ✅ **SPRAWDZENIE CZY WSZYSTKO DZIAŁA**

Po dodaniu zmiennych i redeploy:

### **Test Analytics:**
1. **Otwórz swoją stronę**
2. **Idź do Google Analytics → Real-time**
3. **Powinieneś zobaczyć swoją wizytę**

### **Test Environment Variables:**
1. **Otwórz DevTools (F12)**
2. **Console tab**
3. **Wpisz:** `console.log(process.env.NEXT_PUBLIC_SITE_URL)`
4. **Powinno pokazać:** `https://bakasana-travel.blog`

### **Test Meta Tags:**
1. **Kliknij prawym na stronę → "View Page Source"**
2. **Szukaj:** `<meta property="og:url" content="https://bakasana-travel.blog"`
3. **Sprawdź czy wszystkie meta tagi mają poprawną domenę**

---

## 🚨 **WAŻNE UWAGI**

### **Kolejność dodawania:**
1. **Najpierw** dodaj wszystkie Environment Variables
2. **Potem** redeploy
3. **Na końcu** skonfiguruj DNS domeny

### **Propagacja zmian:**
- Environment Variables: natychmiastowe po redeploy
- DNS: 15 minut - 48 godzin
- SSL: automatyczne po konfiguracji DNS

### **Backup zmiennych:**
Zapisz wszystkie zmienne w bezpiecznym miejscu - będą potrzebne jeśli będziesz migrować projekt.

---

## 🎉 **PO KONFIGURACJI**

Gdy wszystko będzie skonfigurowane, Twoja strona będzie:
- ✅ **Dostępna pod `https://bakasana-travel.blog`**
- ✅ **Trackowana przez Google Analytics**
- ✅ **Zindeksowana przez Google Search**
- ✅ **Zoptymalizowana SEO dla własnej domeny**
- ✅ **Zabezpieczona HTTPS**

**Profesjonalna strona gotowa do działania!** 🌟
