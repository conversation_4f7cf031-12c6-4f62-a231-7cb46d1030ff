'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function PWAInstaller() {
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkIfInstalled = () => {
      const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;
      const isIOSStandalone = window.navigator.standalone === true;
      setIsStandalone(isStandaloneMode || isIOSStandalone);
      setIsInstalled(isStandaloneMode || isIOSStandalone);
    };

    // Check if iOS
    const checkIfIOS = () => {
      const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
      setIsIOS(isIOSDevice);
    };

    // Register service worker
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          console.log('Service Worker registered:', registration);
          
          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New content available, show update prompt
                showUpdatePrompt();
              }
            });
          });
        } catch (error) {
          console.error('Service Worker registration failed:', error);
        }
      }
    };

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setDeferredPrompt(e);
      
      // Show install prompt after user has been on site for a while
      setTimeout(() => {
        if (!isInstalled && !localStorage.getItem('pwa-install-dismissed')) {
          setShowInstallPrompt(true);
        }
      }, 30000); // Show after 30 seconds
    };

    checkIfInstalled();
    checkIfIOS();
    registerServiceWorker();

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, [isInstalled]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      console.log('User accepted the install prompt');
      setIsInstalled(true);
    } else {
      console.log('User dismissed the install prompt');
      localStorage.setItem('pwa-install-dismissed', 'true');
    }
    
    setDeferredPrompt(null);
    setShowInstallPrompt(false);
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    localStorage.setItem('pwa-install-dismissed', 'true');
  };

  const showUpdatePrompt = () => {
    if (confirm('Nowa wersja aplikacji jest dostępna. Czy chcesz ją załadować?')) {
      window.location.reload();
    }
  };

  // Don't show if already installed or on iOS (different install process)
  if (isInstalled || isStandalone) {
    return null;
  }

  // iOS install instructions
  if (isIOS && showInstallPrompt) {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 100 }}
          className="fixed bottom-4 left-4 right-4 z-50 max-w-sm mx-auto"
        >
          <div className="bg-white rounded-2xl shadow-2xl p-6 border border-temple/10">
            <div className="flex items-start gap-4">
              <div className="text-2xl">📱</div>
              <div className="flex-1">
                <h3 className="font-medium text-temple mb-2">
                  Zainstaluj aplikację
                </h3>
                <p className="text-sm text-wood-light mb-4">
                  Dodaj Bali Yoga Journey do ekranu głównego:
                </p>
                <div className="space-y-2 text-xs text-wood-light">
                  <div className="flex items-center gap-2">
                    <span>1.</span>
                    <span>Naciśnij przycisk "Udostępnij" ⬆️</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>2.</span>
                    <span>Wybierz "Dodaj do ekranu głównego" ➕</span>
                  </div>
                </div>
              </div>
              <button
                onClick={handleDismiss}
                className="text-wood-light hover:text-temple transition-colors"
              >
                ✕
              </button>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    );
  }

  // Android/Desktop install prompt
  return (
    <AnimatePresence>
      {showInstallPrompt && deferredPrompt && (
        <motion.div
          initial={{ opacity: 0, y: 100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 100 }}
          className="fixed bottom-4 left-4 right-4 z-50 max-w-sm mx-auto"
        >
          <div className="bg-gradient-to-r from-temple to-golden rounded-2xl shadow-2xl p-6 text-white">
            <div className="flex items-start gap-4">
              <div className="text-2xl">🧘‍♀️</div>
              <div className="flex-1">
                <h3 className="font-medium mb-2">
                  Zainstaluj aplikację Bali Yoga Journey
                </h3>
                <p className="text-sm opacity-90 mb-4">
                  Szybki dostęp do retreatów, map i rezerwacji. Działa offline!
                </p>
                <div className="flex gap-3">
                  <button
                    onClick={handleInstallClick}
                    className="bg-white text-temple px-4 py-2 rounded-lg text-sm font-medium hover:bg-white/90 transition-colors"
                  >
                    Zainstaluj
                  </button>
                  <button
                    onClick={handleDismiss}
                    className="text-white/80 hover:text-white transition-colors text-sm"
                  >
                    Nie teraz
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Hook for checking PWA install status
export function usePWAInstall() {
  const [isInstalled, setIsInstalled] = useState(false);
  const [canInstall, setCanInstall] = useState(false);

  useEffect(() => {
    const checkInstallStatus = () => {
      const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;
      const isIOSStandalone = window.navigator.standalone === true;
      setIsInstalled(isStandaloneMode || isIOSStandalone);
    };

    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setCanInstall(true);
    };

    checkInstallStatus();
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  return { isInstalled, canInstall };
}

// Component for showing PWA features
export function PWAFeatures() {
  const { isInstalled } = usePWAInstall();

  if (!isInstalled) return null;

  return (
    <div className="bg-temple/5 rounded-xl p-6 mb-8">
      <h3 className="text-lg font-serif text-temple mb-4">
        🎉 Aplikacja zainstalowana!
      </h3>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="flex items-center gap-2">
          <span className="text-temple">📱</span>
          <span>Szybki dostęp</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-temple">⚡</span>
          <span>Działa offline</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-temple">🔔</span>
          <span>Powiadomienia</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-temple">💾</span>
          <span>Mniej danych</span>
        </div>
      </div>
    </div>
  );
}
