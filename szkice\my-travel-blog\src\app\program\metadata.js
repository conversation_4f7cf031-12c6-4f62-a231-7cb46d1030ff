export async function generateMetadata() {
    return {
      metadataBase: new URL('https://twoja-strona.pl'),
      title: 'Program Wyjazdu | Bali Yoga Journey',
      description: 'Szczegółowy program wyjazdu na Bali. Codzienne zajęcia jogi, medy<PERSON><PERSON><PERSON>, wycieczki do świątyń, warsztaty kulinarne i więcej.',
      keywords: [
        'program wyjazdu bali',
        'harmonogram jogi',
        'wycieczki bali',
        'medytacja bali',
        'warsztaty kulinarne',
        'świątynie bali'
      ],
      openGraph: {
        title: 'Program Wyjazdu | Bali Yoga Journey',
        description: 'Szczegółowy program wyjazdu na Bali',
        images: [
          {
            url: 'https://twoja-domena.pl/images/program-overview.jpg',
            width: 1200,
            height: 630,
            alt: 'Program zajęć jogi na Bali',
          }
        ],
        type: 'article',
      },
      alternates: {
        canonical: 'https://twoja-domena.pl/program'
      },
      robots: {
        index: true,
        follow: true,
        'max-image-preview': 'large',
        'max-video-preview': -1,
        'max-snippet': -1,
      },
      // Dodajemy strukturowane dane dla programu
      script: [
        {
          type: 'application/ld+json',
          text: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Course',
            name: 'Program Jogi na Bali',
            description: 'Intensywny program jogi i medytacji na Bali',
            provider: {
              '@type': 'Organization',
              name: 'Bali Yoga Journey',
              sameAs: 'https://twoja-domena.pl'
            },
            timeRequired: 'P10D',
            teaches: ['Joga', 'Medytacja', 'Kultura Bali']
          })
        }
      ]
    };
  }