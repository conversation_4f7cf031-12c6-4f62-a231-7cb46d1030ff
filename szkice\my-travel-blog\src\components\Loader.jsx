const Loader = ({ 
  size = 'medium', 
  className = '',
  text = null 
}) => {
  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-10 h-10',
    large: 'w-16 h-16'
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className={`loader ${sizeClasses[size]}`} />
      {text && (
        <p className="mt-4 text-sm font-light tracking-wider uppercase">
          {text}
        </p>
      )}
    </div>
  );
};

// Komponent dla pełnoekranowego loadera
export const FullScreenLoader = ({ text = 'Ładowanie...' }) => {
  return (
    <div className="fixed inset-0 bg-secondary/95 backdrop-blur-sm flex items-center justify-center z-50">
      <Loader size="large" text={text} />
    </div>
  );
};

// Komponent dla inline loadera
export const InlineLoader = ({ text = null }) => {
  return (
    <div className="flex items-center justify-center py-8">
      <Loader size="medium" text={text} />
    </div>
  );
};

export default Loader;
