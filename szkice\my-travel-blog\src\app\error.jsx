'use client';

import { useEffect } from 'react';
import Link from 'next/link';

export default function Error({ error, reset }) {
  useEffect(() => {
    // Bezpieczne logowanie błędu do konsoli
    try {
      if (error) {
        console.warn("--- <PERSON><PERSON><PERSON><PERSON>lik<PERSON>ji Next.js ---");
        if (error.message) {
          console.warn("Wiadomość:", error.message);
        }
        if (error.stack) {
          console.warn("Stos wywołań:", error.stack);
        }
      }
    } catch (loggingError) {
      // Ignoruj błędy logowania
    }
  }, [error]);

  return (
    <main className="py-32 min-h-screen bg-gradient-to-b from-rice via-sand-light/50 to-ocean/10 flex items-center justify-center">
      <div className="max-w-2xl mx-auto px-4 text-center">
        <h1 className="text-5xl md:text-6xl font-display text-temple tracking-tight mb-6">
          Ups!
        </h1>
        <h2 className="text-2xl md:text-3xl font-display text-temple mb-4">
          Co<PERSON> poszło nie tak
        </h2>
        <p className="text-lg text-wood-light/80 mb-8">
          Przepraszamy, wystąpił nieoczekiwany błąd. Nasz zespół został powiadomiony.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => reset()}
            className="inline-flex items-center justify-center px-6 py-3 bg-temple text-rice rounded-full hover:bg-temple/90 transition-colors"
          >
            Spróbuj ponownie
          </button>
          <Link
            href="/"
            className="inline-flex items-center justify-center px-6 py-3 bg-rice text-temple border border-temple/20 rounded-full hover:bg-rice/80 transition-colors"
          >
            Wróć na stronę główną
          </Link>
        </div>
      </div>
    </main>
  );
}