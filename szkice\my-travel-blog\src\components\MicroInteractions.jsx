'use client';

import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Ripple Effect Button
export const RippleButton = ({ 
  children, 
  onClick, 
  className = '', 
  variant = 'primary',
  disabled = false,
  ...props 
}) => {
  const [ripples, setRipples] = useState([]);
  const buttonRef = useRef(null);

  const createRipple = (event) => {
    const button = buttonRef.current;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    const newRipple = {
      x,
      y,
      size,
      id: Date.now(),
    };

    setRipples(prev => [...prev, newRipple]);

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
    }, 600);

    if (onClick) onClick(event);
  };

  const baseClasses = "relative overflow-hidden transition-all duration-300 transform";
  const variantClasses = {
    primary: "bg-temple/10 hover:bg-temple/20 border border-temple/30 hover:border-temple/50 text-temple",
    secondary: "bg-white/30 hover:bg-white/50 border border-white/30 hover:border-white/50 text-temple",
    ghost: "bg-transparent hover:bg-temple/10 text-temple/80 hover:text-temple"
  };

  return (
    <motion.button
      ref={buttonRef}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={createRipple}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      {...props}
    >
      {children}
      
      {/* Ripple effects */}
      <AnimatePresence>
        {ripples.map((ripple) => (
          <motion.span
            key={ripple.id}
            initial={{ scale: 0, opacity: 0.6 }}
            animate={{ scale: 2, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="absolute rounded-full bg-white/30 pointer-events-none"
            style={{
              left: ripple.x,
              top: ripple.y,
              width: ripple.size,
              height: ripple.size,
            }}
          />
        ))}
      </AnimatePresence>
    </motion.button>
  );
};

// Mandala Loading Spinner
export const MandalaLoader = ({ size = 40, className = '' }) => {
  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      <motion.svg
        animate={{ rotate: 360 }}
        transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
        width={size}
        height={size}
        viewBox="0 0 100 100"
        className="text-temple"
      >
        <circle
          cx="50"
          cy="50"
          r="40"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeDasharray="10,5"
          opacity="0.3"
        />
        <circle
          cx="50"
          cy="50"
          r="25"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeDasharray="5,3"
          opacity="0.5"
        />
        <motion.circle
          animate={{ rotate: -360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          cx="50"
          cy="50"
          r="10"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
          strokeDasharray="3,2"
          style={{ transformOrigin: '50px 50px' }}
        />
      </motion.svg>
    </div>
  );
};

// Floating Card with Levitation Effect
export const LevitatingCard = ({ 
  children, 
  className = '', 
  hoverHeight = 15,
  rotationIntensity = 5 
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  const handleMouseMove = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = (e.clientX - rect.left - rect.width / 2) / rect.width;
    const y = (e.clientY - rect.top - rect.height / 2) / rect.height;
    setMousePosition({ x, y });
  };

  return (
    <motion.div
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      animate={{
        y: isHovering ? -hoverHeight : 0,
        rotateX: isHovering ? mousePosition.y * rotationIntensity : 0,
        rotateY: isHovering ? mousePosition.x * rotationIntensity : 0,
      }}
      transition={{ 
        type: "spring", 
        stiffness: 300, 
        damping: 30 
      }}
      className={`transform-gpu perspective-1000 ${className}`}
      style={{ transformStyle: 'preserve-3d' }}
    >
      {children}
    </motion.div>
  );
};

// Success Flash Effect
export const SuccessFlash = ({ isVisible, onComplete }) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: [0, 1, 0] }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          onAnimationComplete={onComplete}
          className="fixed inset-0 bg-white z-50 pointer-events-none"
        />
      )}
    </AnimatePresence>
  );
};

// Breathing Input Field
export const BreathingInput = ({ 
  label, 
  type = 'text', 
  value, 
  onChange, 
  placeholder,
  className = '',
  ...props 
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div className={`relative ${className}`}>
      <motion.div
        animate={{
          scale: isFocused ? [1, 1.02, 1] : 1,
        }}
        transition={{
          duration: 4,
          repeat: isFocused ? Infinity : 0,
          ease: "easeInOut",
        }}
        className="relative"
      >
        <input
          type={type}
          value={value}
          onChange={onChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          className="w-full px-6 py-4 bg-white/70 backdrop-blur-sm border border-temple/20 rounded-full text-temple placeholder-temple/50 focus:outline-none focus:border-temple/50 focus:bg-white/90 transition-all duration-300"
          {...props}
        />
        
        {/* Breathing glow effect */}
        <motion.div
          animate={{
            opacity: isFocused ? [0, 0.3, 0] : 0,
            scale: isFocused ? [1, 1.1, 1] : 1,
          }}
          transition={{
            duration: 4,
            repeat: isFocused ? Infinity : 0,
            ease: "easeInOut",
          }}
          className="absolute inset-0 rounded-full bg-temple/10 blur-sm pointer-events-none"
        />
      </motion.div>
      
      {label && (
        <motion.label
          animate={{
            y: isFocused || value ? -25 : 0,
            scale: isFocused || value ? 0.85 : 1,
            color: isFocused ? '#8B7355' : '#8B735580',
          }}
          transition={{ duration: 0.2 }}
          className="absolute left-6 top-4 pointer-events-none origin-left"
        >
          {label}
        </motion.label>
      )}
    </div>
  );
};

// Zen Quote Tooltip
export const ZenTooltip = ({ children, quote, author }) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-4 py-3 bg-temple/90 text-white text-sm rounded-xl backdrop-blur-sm max-w-xs text-center"
          >
            <p className="italic mb-1">"{quote}"</p>
            <p className="text-xs opacity-80">— {author}</p>
            
            {/* Arrow */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-temple/90" />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default {
  RippleButton,
  MandalaLoader,
  LevitatingCard,
  SuccessFlash,
  BreathingInput,
  ZenTooltip,
};
