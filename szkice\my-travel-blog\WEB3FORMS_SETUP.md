# 📧 KONFIGURACJA FORMULARZA KONTAKTOWEGO - WEB3FORMS

## 🚀 **SZYBKA KONFIGURACJA (5 minut)**

### **Krok 1: Z<PERSON><PERSON><PERSON><PERSON> konto na Web3Forms**
1. Id<PERSON> na: https://web3forms.com
2. Kliknij **"Get Started Free"**
3. Zarejestruj się emailem: `<EMAIL>`
4. Potwierdź email

### **Krok 2: Stwórz formularz**
1. Po zalogowaniu kliknij **"Create New Form"**
2. Nazwa formularza: `Bali Yoga Journey - Kontakt`
3. Email docelowy: `<EMAIL>`
4. Skopiuj **Access Key** (np. `a1b2c3d4-e5f6-7890-abcd-ef1234567890`)

### **Krok 3: Zaktualizuj kod**
W pliku `src/app/kontakt/ContactForm.jsx` znajd<PERSON> linię:
```javascript
access_key: 'YOUR_WEB3FORMS_ACCESS_KEY',
```

Zastąp `YOUR_WEB3FORMS_ACCESS_KEY` swoim kluczem:
```javascript
access_key: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
```

### **Krok 4: Testuj formularz**
1. Wejdź na stronę `/kontakt`
2. Wypełnij formularz
3. Wyślij testową wiadomość
4. Sprawdź email `<EMAIL>`

---

## ✅ **CO MASZ JUŻ GOTOWE**

### **Zabezpieczenia:**
- ✅ **Honeypot** - ochrona przed spam botami
- ✅ **Walidacja** - sprawdzanie poprawności danych
- ✅ **Rate limiting** - Web3Forms automatycznie ogranicza spam

### **UX/UI:**
- ✅ **Loading state** - przycisk pokazuje "Wysyłanie..."
- ✅ **Success/Error messages** - jasne komunikaty
- ✅ **Responsive design** - działa na mobile
- ✅ **Accessibility** - labels, aria-labels

### **Funkcjonalność:**
- ✅ **Automatyczny subject** - "Nowa wiadomość z Bali Yoga Journey od [Imię]"
- ✅ **From name** - "Bali Yoga Journey"
- ✅ **Clean reset** - formularz czyści się po wysłaniu

---

## 🎯 **LIMITY DARMOWEGO PLANU**

- **250 wiadomości/miesiąc** - wystarczy na start
- **Bez reklam** w emailach
- **Podstawowe analytics**
- **Email notifications**

Jeśli przekroczysz limit, plan Pro kosztuje $3/miesiąc (750 wiadomości).

---

## 🔧 **OPCJONALNE ULEPSZENIA**

### **1. Dodaj więcej pól (opcjonalnie):**
```javascript
// W formularzu dodaj:
phone: formData.phone,
retreat_interest: formData.retreat_interest,
```

### **2. Autoresponder (w Web3Forms dashboard):**
```
Dziękujemy za wiadomość! 

Otrzymaliśmy Twoje zapytanie dotyczące retreatów jogowych na Bali. 
Odpowiemy w ciągu 24 godzin.

Namaste,
Julia Jakubowicz
Bali Yoga Journey
```

### **3. Webhook (zaawansowane):**
Możesz dodać webhook do Zapier/Make.com dla automatyzacji.

---

## 🚨 **TROUBLESHOOTING**

### **Problem: "Access key not found"**
- Sprawdź czy skopiowałaś cały klucz
- Upewnij się, że nie ma spacji na początku/końcu

### **Problem: Nie dostaje emaili**
- Sprawdź folder SPAM
- Sprawdź czy email w Web3Forms to `<EMAIL>`
- Sprawdź czy formularz jest aktywny w dashboard

### **Problem: "CORS error"**
- Web3Forms automatycznie obsługuje CORS
- Jeśli problem persystuje, sprawdź czy domena jest dodana w ustawieniach

---

## 📊 **MONITORING**

W Web3Forms dashboard możesz:
- ✅ Zobacz wszystkie wysłane wiadomości
- ✅ Sprawdzić statystyki
- ✅ Zarządzać ustawieniami
- ✅ Dodać więcej formularzy

---

## 🎉 **GOTOWE!**

Po konfiguracji formularz będzie:
1. ✅ Wysyłać emaile na `<EMAIL>`
2. ✅ Chronić przed spamem
3. ✅ Pokazywać ładne komunikaty
4. ✅ Działać na wszystkich urządzeniach

**Czas konfiguracji: 5 minut**
**Koszt: 0 PLN (do 250 wiadomości/miesiąc)**

---

## 📞 **POTRZEBUJESZ POMOCY?**

Jeśli coś nie działa:
1. Sprawdź ten przewodnik ponownie
2. Sprawdź Web3Forms documentation: https://docs.web3forms.com
3. Napisz do mnie z opisem problemu

**Powodzenia! 🚀**
