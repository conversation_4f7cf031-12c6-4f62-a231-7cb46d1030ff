import React from 'react';

const TrustBadges = () => {
  const badges = [
    {
      id: 'participants',
      value: '47+',
      label: 'Uczestników',
      description: 'Zadowolonych klientów'
    },
    {
      id: 'rating',
      value: '4.9★',
      label: 'Średnia ocen',
      description: 'Na podstawie opinii'
    },
    {
      id: 'safety',
      value: '100%',
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      description: 'Sprawdzone miejsca'
    },
    {
      id: 'experience',
      value: '5+',
      label: 'Lat doświadczenia',
      description: 'W prowadzeniu retreatów'
    }
  ];

  return (
    <section className="py-12 bg-gradient-to-r from-shell/30 to-bamboo/20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-serif text-temple mb-2"><PERSON><PERSON>czego warto nam zau<PERSON>ć?</h3>
          <p className="text-wood-light/80 font-light">Sprawdzone doświadczenie i zadowoleni klienci</p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {badges.map((badge, index) => (
            <div 
              key={badge.id}
              className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-temple/10 hover:border-temple/20 transition-all duration-300 hover:scale-105"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="text-3xl lg:text-4xl font-light text-temple mb-2 font-serif">
                {badge.value}
              </div>
              <div className="text-sm font-medium text-temple mb-1">
                {badge.label}
              </div>
              <div className="text-xs text-wood-light/60">
                {badge.description}
              </div>
            </div>
          ))}
        </div>
        
        {/* Dodatkowe elementy zaufania */}
        <div className="mt-8 flex flex-wrap justify-center items-center gap-6 text-sm text-wood-light/70">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Certyfikowana instruktorka RYT 500</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Magister fizjoterapii</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Ubezpieczenie NNW</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Małe grupy (max 12 osób)</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustBadges;
