globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/rezerwacja/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs":{"*":{"id":"(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/react/index.mjs":{"*":{"id":"(ssr)/./node_modules/@vercel/speed-insights/dist/react/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AsyncCSS.jsx":{"*":{"id":"(ssr)/./src/components/AsyncCSS.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientAnalytics.jsx":{"*":{"id":"(ssr)/./src/components/ClientAnalytics.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientResourcePreloader.jsx":{"*":{"id":"(ssr)/./src/components/ClientResourcePreloader.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CookieConsent.jsx":{"*":{"id":"(ssr)/./src/components/CookieConsent.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/PWAInstaller.jsx":{"*":{"id":"(ssr)/./src/components/PWAInstaller.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SmoothScroll.jsx":{"*":{"id":"(ssr)/./src/components/SmoothScroll.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/WebVitalsMonitor.jsx":{"*":{"id":"(ssr)/./src/components/WebVitalsMonitor.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.jsx":{"*":{"id":"(ssr)/./src/app/error.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx":{"*":{"id":"(ssr)/./src/app/blog/BlogPageClientContent.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/BookingCalendar.jsx":{"*":{"id":"(ssr)/./src/components/BookingCalendar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/FAQSection.jsx":{"*":{"id":"(ssr)/./src/components/FAQSection.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/NewsletterSignup.jsx":{"*":{"id":"(ssr)/./src/components/NewsletterSignup.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ScrollReveal.jsx":{"*":{"id":"(ssr)/./src/components/ScrollReveal.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/InteractiveMap.jsx":{"*":{"id":"(ssr)/./src/components/InteractiveMap.jsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\@vercel\\analytics\\dist\\react\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\@vercel\\speed-insights\\dist\\react\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/react/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.jsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\",\"weight\":[\"300\",\"400\",\"500\"],\"display\":\"swap\"}],\"variableName\":\"montserrat\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.jsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\",\"weight\":[\"300\",\"400\",\"500\"],\"display\":\"swap\"}],\"variableName\":\"montserrat\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.jsx\",\"import\":\"Cormorant_Garamond\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-cormorant\",\"weight\":[\"300\",\"400\"],\"display\":\"swap\"}],\"variableName\":\"cormorant\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.jsx\",\"import\":\"Cormorant_Garamond\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-cormorant\",\"weight\":[\"300\",\"400\"],\"display\":\"swap\"}],\"variableName\":\"cormorant\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\AsyncCSS.jsx":{"id":"(app-pages-browser)/./src/components/AsyncCSS.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\ClientAnalytics.jsx":{"id":"(app-pages-browser)/./src/components/ClientAnalytics.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\ClientResourcePreloader.jsx":{"id":"(app-pages-browser)/./src/components/ClientResourcePreloader.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\CookieConsent.jsx":{"id":"(app-pages-browser)/./src/components/CookieConsent.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\PWAInstaller.jsx":{"id":"(app-pages-browser)/./src/components/PWAInstaller.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\SmoothScroll.jsx":{"id":"(app-pages-browser)/./src/components/SmoothScroll.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\WebVitalsMonitor.jsx":{"id":"(app-pages-browser)/./src/components/WebVitalsMonitor.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\app\\error.jsx":{"id":"(app-pages-browser)/./src/app/error.jsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\app\\blog\\BlogPageClientContent.jsx":{"id":"(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\BookingCalendar.jsx":{"id":"(app-pages-browser)/./src/components/BookingCalendar.jsx","name":"*","chunks":["app/rezerwacja/page","static/chunks/app/rezerwacja/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\FAQSection.jsx":{"id":"(app-pages-browser)/./src/components/FAQSection.jsx","name":"*","chunks":["app/rezerwacja/page","static/chunks/app/rezerwacja/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\NewsletterSignup.jsx":{"id":"(app-pages-browser)/./src/components/NewsletterSignup.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\ScrollReveal.jsx":{"id":"(app-pages-browser)/./src/components/ScrollReveal.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\components\\InteractiveMap.jsx":{"id":"(app-pages-browser)/./src/components/InteractiveMap.jsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\app\\not-found":[],"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\app\\error":[],"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\szkice\\my-travel-blog\\src\\app\\rezerwacja\\page":[{"inlined":false,"path":"static/css/app/rezerwacja/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs":{"*":{"id":"(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/react/index.mjs":{"*":{"id":"(rsc)/./node_modules/@vercel/speed-insights/dist/react/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AsyncCSS.jsx":{"*":{"id":"(rsc)/./src/components/AsyncCSS.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientAnalytics.jsx":{"*":{"id":"(rsc)/./src/components/ClientAnalytics.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientResourcePreloader.jsx":{"*":{"id":"(rsc)/./src/components/ClientResourcePreloader.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CookieConsent.jsx":{"*":{"id":"(rsc)/./src/components/CookieConsent.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/PWAInstaller.jsx":{"*":{"id":"(rsc)/./src/components/PWAInstaller.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SmoothScroll.jsx":{"*":{"id":"(rsc)/./src/components/SmoothScroll.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/WebVitalsMonitor.jsx":{"*":{"id":"(rsc)/./src/components/WebVitalsMonitor.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.jsx":{"*":{"id":"(rsc)/./src/app/error.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx":{"*":{"id":"(rsc)/./src/app/blog/BlogPageClientContent.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/BookingCalendar.jsx":{"*":{"id":"(rsc)/./src/components/BookingCalendar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/FAQSection.jsx":{"*":{"id":"(rsc)/./src/components/FAQSection.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/NewsletterSignup.jsx":{"*":{"id":"(rsc)/./src/components/NewsletterSignup.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ScrollReveal.jsx":{"*":{"id":"(rsc)/./src/components/ScrollReveal.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/InteractiveMap.jsx":{"*":{"id":"(rsc)/./src/components/InteractiveMap.jsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}