import Image from 'next/image';

export default function ZajeciaOnlinePage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Zajęcia Jogi Online - <PERSON>",
    "description": "Prywatne lekcje jogi online i grupowe zajęcia online z certyfikowaną instruktorką jogi RYT 500.",
    "provider": {
      "@type": "Person",
      "name": "<PERSON>"
    }
  };

  const privateClasses = {
    title: "Prywatne Lekcje Jogi Online (1:1)",
    description: "Spersonalizowane sesje jogi dostosowane do Twoich potrzeb i poziomu zaawansowania",
    features: [
      "Konsultacja wstępna i analiza potrzeb",
      "Spersonalizowany program ćwiczeń", 
      "Korekty w czasie rzeczywistym",
      "Nagranie sesji do powtarzania",
      "Plan domowej praktyki między sesjami"
    ],
    pricing: [
      { name: "<PERSON>sja próbna", duration: "30 min", price: "80 zł" },
      { name: "Pojedyncza sesja", duration: "60 min", price: "150 zł" },
      { name: "Pakiet 4 sesji", duration: "60 min", price: "550 zł", savings: "Oszczędność 50 zł" },
      { name: "Pakiet 8 sesji", duration: "60 min", price: "1000 zł", savings: "Oszczędność 200 zł" }
    ]
  };

  const groupClasses = {
    title: "Grupowe Zajęcia Online",
    description: "Zajęcia jogi w małych grupach dla lepszej atmosfery i indywidualnego podejścia",
    schedule: [
      { day: "Poniedziałek", time: "18:00", type: "Hatha Yoga", level: "początkujący" },
      { day: "Środa", time: "19:00", type: "Vinyasa Flow", level: "średniozaawansowani" },
      { day: "Piątek", time: "17:30", type: "Yin Yoga", level: "relaks" },
      { day: "Sobota", time: "10:00", type: "Joga dla pleców", level: "wszystkie poziomy" }
    ],
    pricing: [
      { name: "Małe grupy", size: "4-6 osób", price: "45 zł/zajęcia" },
      { name: "Średnie grupy", size: "7-12 osób", price: "35 zł/zajęcia" },
      { name: "Karnet miesięczny", size: "8 zajęć", price: "280 zł" },
      { name: "Karnet kwartalny", size: "24 zajęcia", price: "720 zł" }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <main className="relative min-h-screen bg-gradient-to-b from-rice/90 via-mist/50 to-ocean-light/10">
        {/* Hero Section */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="section-title">
              <h1 className="text-4xl md:text-5xl font-serif text-temple mb-6 font-light">
                Zajęcia Online
              </h1>
              <p className="text-lg text-wood-light leading-relaxed font-light">
                Praktykuj jogę z domu w komforcie i bezpieczeństwie
              </p>
              <div className="decorative-line" />
            </div>
          </div>
        </section>

        {/* Private Classes Section */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="card p-12">
              <div className="md:flex items-start gap-12">
                {/* Content */}
                <div className="md:w-3/5 mb-8 md:mb-0">
                  <h2 className="text-3xl font-serif text-temple mb-4 font-light">
                    Prywatne Lekcje Jogi Online
                  </h2>

                  <p className="text-wood-light leading-relaxed mb-8 font-light">
                    Spersonalizowane sesje jogi dostosowane do Twoich potrzeb i poziomu zaawansowania.
                  </p>

                  {/* Features */}
                  <div className="mb-8">
                    <h3 className="text-lg text-temple mb-4 font-light">Co obejmuje sesja:</h3>
                    <div className="space-y-3">
                      {privateClasses.features.map((feature, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <div className="w-1 h-1 bg-temple/40 mt-2 flex-shrink-0"></div>
                          <span className="text-wood-light leading-relaxed text-sm font-light">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Pricing */}
                  <div>
                    <h3 className="text-lg text-temple mb-4 font-light">Cennik:</h3>
                    <div className="space-y-3">
                      {privateClasses.pricing.map((option, index) => (
                        <div key={index} className="flex items-center justify-between py-2 border-b border-temple/10">
                          <div>
                            <h4 className="text-temple font-light">{option.name}</h4>
                            <p className="text-xs text-wood-light">{option.duration}</p>
                          </div>
                          <div className="text-right">
                            <span className="text-temple font-light">{option.price}</span>
                            {option.savings && (
                              <p className="text-xs text-sage">{option.savings}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Image */}
                <div className="md:w-2/5">
                  <div className="h-80 md:h-96 relative rounded-lg overflow-hidden">
                    <Image
                      src="/images/profile/omnie-opt.webp"
                      alt="Prywatne lekcje jogi online"
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 40vw"
                    />
                    <div className="absolute inset-0 bg-temple/5" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Group Classes Section */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="card p-12">
              <div className="md:flex items-start gap-12 md:flex-row-reverse">
                {/* Content */}
                <div className="md:w-3/5 mb-8 md:mb-0">
                  <h2 className="text-3xl font-serif text-temple mb-4 font-light">
                    Grupowe Zajęcia Online
                  </h2>

                  <p className="text-wood-light leading-relaxed mb-8 font-light">
                    Zajęcia jogi w małych grupach dla lepszej atmosfery i indywidualnego podejścia.
                  </p>

                  {/* Schedule */}
                  <div className="mb-8">
                    <h3 className="text-lg text-temple mb-4 font-light">Harmonogram zajęć:</h3>
                    <div className="space-y-3">
                      {groupClasses.schedule.map((session, index) => (
                        <div key={index} className="flex items-center justify-between py-2 border-b border-temple/10">
                          <div>
                            <h4 className="text-temple font-light">{session.day} {session.time}</h4>
                            <p className="text-xs text-wood-light">{session.type}</p>
                          </div>
                          <span className="text-xs text-golden/80 bg-golden/5 px-2 py-1 rounded-full">
                            {session.level}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Pricing */}
                  <div>
                    <h3 className="text-lg text-temple mb-4 font-light">Opcje cenowe:</h3>
                    <div className="space-y-3">
                      {groupClasses.pricing.map((option, index) => (
                        <div key={index} className="flex items-center justify-between py-2 border-b border-temple/10">
                          <div>
                            <h4 className="text-temple font-light">{option.name}</h4>
                            <p className="text-xs text-wood-light">{option.size}</p>
                          </div>
                          <span className="text-temple font-light">{option.price}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Image */}
                <div className="md:w-2/5">
                  <div className="h-80 md:h-96 bg-temple/5 flex items-center justify-center rounded-lg">
                    <div className="text-center">
                      <div className="text-4xl mb-2 text-temple/40">🧘‍♀️</div>
                      <span className="text-temple/60 text-lg font-serif font-light">Zajęcia Grupowe</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="section section-padding">
          <div className="max-width-content container-padding">
            <div className="glass-effect bg-gradient-to-r from-bamboo/10 to-lotus/20 rounded-3xl p-12 text-center">
              <h3 className="text-2xl font-serif text-temple mb-6 font-light">
                Gotowa na pierwszą sesję?
              </h3>

              <p className="text-wood-light leading-relaxed mb-8 font-light">
                Skontaktuj się ze mną, aby umówić pierwszą sesję lub dołączyć do zajęć grupowych.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a
                  href="https://app.fitssey.com/Flywithbakasana/frontoffice"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn-soft-golden"
                  aria-label="Umów sesję próbną przez Fitssey"
                >
                  Umów Sesję przez Fitssey
                </a>

                <span className="text-temple/50 text-sm">lub</span>

                <a
                  href="/o-mnie"
                  className="btn-soft"
                  aria-label="Poznaj instruktorkę"
                >
                  Poznaj Instruktorkę
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>
    </>
  );
}
