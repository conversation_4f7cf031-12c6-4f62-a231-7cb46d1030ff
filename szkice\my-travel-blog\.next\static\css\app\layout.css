/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: var(--font-montserrat), system-ui, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  :root {
    /* Elegancka, minimalistyczna paleta - tylko 5 głównych kolorów */
    --color-primary: 26 26 26;         /* Głęboka czerń dla tekstu */
    --color-secondary: 248 246 243;    /* Kremowa biel dla tła */
    --color-accent: 139 115 85;        /* Ciepły brąz */
    --color-soft-sage: 232 229 224;    /* Bardzo delikatny beż */
    --color-warm-gold: 212 165 116;    /* Złoty dla akcentów */

    /* Fonty - wyrafinowana typografia */
    --font-montserrat: 'Montserrat', 'system-ui', '-apple-system', 'BlinkMacSystemFont', sans-serif;
    --font-cormorant: 'Cormorant Garamond', 'Georgia', 'Times New Roman', serif;
    
    /* Cienie - profesjonalne, bardzo subtelne */
    --shadow-soft: 0 1px 3px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 2px 6px rgba(0, 0, 0, 0.06);
    --shadow-subtle: 0 1px 2px rgba(0, 0, 0, 0.02);

    
    /* Timing functions */
    --ease-gentle: cubic-bezier(0.23, 1, 0.32, 1);
    --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
    --ease-out-smooth: cubic-bezier(0.16, 1, 0.3, 1);
  }

  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
  font-family: var(--font-montserrat), system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
    background: rgb(var(--color-secondary));
    color: rgb(var(--color-primary));
    letter-spacing: 0.01em;
    line-height: 1.75;
    overflow-x: hidden;
}

  h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-cormorant), Georgia, serif;
    color: rgb(var(--color-primary));
    font-weight: 300;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

  h1 {
    font-size: clamp(2.5rem, 5vw, 4.5rem);
    font-weight: 300;
    letter-spacing: -0.03em;
    line-height: 1.1;
  }

  h2 {
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 300;
    letter-spacing: -0.02em;
    line-height: 1.2;
  }

  h3 {
    font-size: clamp(1.5rem, 3vw, 2.5rem);
    font-weight: 300;
    letter-spacing: -0.01em;
    line-height: 1.3;
  }

  h4 {
  font-size: 1.125rem;
  line-height: 2rem;
}

  @media (min-width: 1024px) {

  h4 {
    font-size: 1.25rem;
    line-height: 2.1rem;
    letter-spacing: -0.01em;
  }
}

  h4 {
    font-weight: 300;
  }

  /* Vertical text utility */

  /* Outline text utility */
  .text-outline {
    -webkit-text-stroke: 1px rgb(var(--color-primary));
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  /* Mixed sizes in sentences */

  /* Section numbers in background */
  .section-number {
    position: absolute;
    top: 2rem;
    left: 2rem;
    font-size: clamp(6rem, 10vw, 10rem);
    font-weight: 100;
    color: rgb(var(--color-primary) / 0.03);
    font-family: var(--font-cormorant);
    line-height: 1;
    z-index: 0;
    pointer-events: none;
    transition: color 0.3s ease;
  }

  .section:hover .section-number {
    color: rgb(var(--color-warm-gold) / 0.05);
  }

  /* Thin divider lines */
  .divider-line {
    height: 1px;
    background: linear-gradient(to right, transparent, rgb(var(--color-primary) / 0.1), transparent);
    margin: 4rem 0;
  }

  /* Elegancki separator między sekcjami */
  .section::after {
    content: '';
    display: block;
    width: 80px;
    height: 1px;
    margin: 4rem auto;
    background: linear-gradient(
      to right,
      transparent,
      rgb(var(--color-accent)),
      transparent
    );
  }

  /* Alternatywnie - symbol lotosu */
  .section.lotus-divider::after {
    content: '❀';
    font-size: 2rem;
    color: rgb(var(--color-warm-gold));
    height: auto;
    background: none;
    text-align: center;
    width: auto;
    line-height: 1;
  }

  /* Huge quotes z custom quotation marks */

  /* Pierwsza litera w sekcjach */
  .section-content p:first-of-type::first-letter {
    font-family: var(--font-cormorant);
    font-size: 4.5rem;
    line-height: 1;
    float: left;
    margin: 0 0.5rem 0 0;
    color: rgb(var(--color-accent));
    font-weight: 300;
  }

  /* Date/location styling */
  .meta-text {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.2em;
    font-weight: 400;
    color: rgb(var(--color-primary) / 0.6);
  }

  /* Eleganckie podtytuły */
  .subtitle {
  font-size: 0.875rem;
  line-height: 1.6rem;
  text-transform: uppercase;
    color: rgb(var(--color-accent) / 0.7);
    letter-spacing: 0.1em;
    font-weight: 400;
}

  p {
  font-size: 1rem;
  line-height: 1.8rem;
  line-height: 1.625;
    color: rgb(var(--color-primary) / 0.8);
    margin-bottom: 1.5rem;
    font-weight: 300;
}

  p:last-child {
    margin-bottom: 0;
  }

  a {
    color: rgb(var(--color-accent));
    text-decoration: none;
    position: relative;
    font-weight: 300;
    transition: all 0.3s ease;
  }

  a:hover {
    color: rgb(var(--color-primary));
    transform: translateY(-1px);
  }

  ::-moz-selection {
    color: rgb(var(--color-primary));
    background-color: rgb(var(--color-soft-sage) / 0.3);
  }

  ::selection {
    color: rgb(var(--color-primary));
    background-color: rgb(var(--color-soft-sage) / 0.3);
  }

  img {
  display: block;
  height: auto;
  max-width: 100%;
    image-rendering: -webkit-optimize-contrast;
}

  /* Image lazy loading z fade */

  .lazy-image.loaded {
    opacity: 1;
  }

  /* Placeholder blur */

  @keyframes shimmer {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
  }

  ::-webkit-scrollbar {
  width: 0.5rem;
}

  ::-webkit-scrollbar-track {
  background-color: transparent;
}

  ::-webkit-scrollbar-thumb {
  border-radius: 9999px;
    background-color: rgb(var(--color-accent) / 0.20);
    -webkit-transition: background-color 0.3s ease;
    transition: background-color 0.3s ease;
}

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgb(var(--color-accent) / 0.40);
  }
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
/* Minimalistyczne przyciski z micro-animations */
.btn-primary {
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.6rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
    background: transparent;
    border: 1px solid rgb(var(--color-primary));
    color: rgb(var(--color-primary));
    transition: all 0.3s ease;
    font-weight: 400;
    position: relative;
    overflow: hidden;
}
.btn-primary::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }
.btn-primary:hover {
    background: rgb(var(--color-primary));
    color: rgb(var(--color-secondary));
  }
.btn-primary:hover::before {
    width: 300px;
    height: 300px;
  }
/* Profesjonalne karty z głębią */
.card {
    background: rgba(248, 246, 243, 0.95);
    border: 1px solid rgba(232, 229, 224, 0.5);
    box-shadow: var(--shadow-soft);
    transition: all 0.5s var(--ease-gentle);
    overflow: hidden;
    position: relative;
  }
.card::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, rgb(var(--color-warm-gold) / 0.3), transparent, rgb(var(--color-accent) / 0.2));
    opacity: 0;
    z-index: -1;
    filter: blur(10px);
    transition: opacity 0.5s ease;
    border-radius: inherit;
  }
.card:hover {
    transform: translateY(-4px);
    box-shadow:
      0 10px 30px rgba(139, 115, 85, 0.15),
      0 1px 4px rgba(139, 115, 85, 0.1);
  }
.card:hover::after {
    opacity: 1;
  }
.card:hover .card-image img {
  --tw-scale-x: 1.01;
  --tw-scale-y: 1.01;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
/* Profesjonalna nawigacja */
.navbar {
  position: fixed;
  top: 0px;
  width: 100%;
    background: rgba(248, 246, 243, 0.95);
    -webkit-backdrop-filter: blur(8px);
            backdrop-filter: blur(8px);
    border-bottom: 1px solid rgba(26, 26, 26, 0.08);
    z-index: 50;
    transition: all 0.3s ease;
}
.navbar.scrolled {
    background: rgba(248, 246, 243, 0.98);
    -webkit-backdrop-filter: blur(12px);
            backdrop-filter: blur(12px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);
  }
.nav-link {
  position: relative;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
    color: rgb(var(--color-primary) / 0.7);
    font-weight: 300;
}
.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    width: 0;
    height: 2px;
    background: rgb(var(--color-accent));
    transform: translateX(-50%);
    transition: width 0.3s ease;
  }
.nav-link:hover {
    color: rgb(var(--color-primary));
  }
.nav-link:hover::after,
  .nav-link[aria-current="page"]::after {
    width: 100%;
  }
.nav-link[aria-current="page"] {
    color: rgb(var(--color-primary));
  }
/* Delikatne cząsteczki kurzu/światła */
@keyframes float {
    0% { transform: translateY(0) translateX(0) rotate(0deg); }
    33% { transform: translateY(-30px) translateX(20px) rotate(120deg); }
    66% { transform: translateY(-60px) translateX(-10px) rotate(240deg); }
    100% { transform: translateY(-100px) translateX(30px) rotate(360deg); }
  }
.section {
  position: relative;
  padding-top: 6rem;
  padding-bottom: 6rem;
}
@media (min-width: 1024px) {

  .section {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}
.section-title {
  margin-bottom: 4rem;
  text-align: center;
}
.section-title h2 {
  margin-bottom: 1.5rem;
    color: rgb(var(--color-primary));
}
.section-title p {
  margin-left: auto;
  margin-right: auto;
  max-width: 48rem;
  font-size: 1.125rem;
  line-height: 2rem;
    color: rgb(var(--color-primary) / 0.7);
}
.decorative-line {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 2rem;
  height: 1px;
  width: 6rem;
    background: linear-gradient(to right, transparent, rgb(var(--color-accent) / 0.30), transparent);
}
.glass-effect {
  border-radius: 0.5rem;
  border-width: 1px;
    background-color: rgba(248, 246, 243, 0.95);
    border-color: rgba(232, 229, 224, 0.5);
}
.gradient-text {
    background: linear-gradient(to right, rgb(var(--color-accent)), rgb(var(--color-warm-gold)));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
.group:hover .image-hover-zoom img {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
/* Enhanced section styling */
/* Gradient text effects */
/* Enhanced decorative elements */
/* Floating animation for badges */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
  }
/* Enhanced hover effects */
/* Subtle glow effects */
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.-left-1 {
  left: -0.25rem;
}
.-top-2 {
  top: -0.5rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-6 {
  bottom: 1.5rem;
}
.bottom-full {
  bottom: 100%;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.left-8 {
  left: 2rem;
}
.right-0 {
  right: 0px;
}
.right-4 {
  right: 1rem;
}
.right-6 {
  right: 1.5rem;
}
.right-8 {
  right: 2rem;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-3 {
  top: 0.75rem;
}
.top-4 {
  top: 1rem;
}
.top-8 {
  top: 2rem;
}
.top-full {
  top: 100%;
}
.z-10 {
  z-index: 10;
}
.z-50 {
  z-index: 50;
}
.col-span-full {
  grid-column: 1 / -1;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-16 {
  margin-top: 4rem;
  margin-bottom: 4rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-24 {
  margin-bottom: 6rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mt-0 {
  margin-top: 0px;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-20 {
  margin-top: 5rem;
}
.mt-24 {
  margin-top: 6rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.h-0 {
  height: 0px;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[240px\] {
  height: 240px;
}
.h-\[300px\] {
  height: 300px;
}
.h-\[40vh\] {
  height: 40vh;
}
.h-\[600px\] {
  height: 600px;
}
.h-\[60vh\] {
  height: 60vh;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-0 {
  max-height: 0px;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.min-h-\[260px\] {
  min-height: 260px;
}
.min-h-\[280px\] {
  min-height: 280px;
}
.min-h-\[300px\] {
  min-height: 300px;
}
.min-h-\[320px\] {
  min-height: 320px;
}
.min-h-\[400px\] {
  min-height: 400px;
}
.min-h-\[60px\] {
  min-height: 60px;
}
.min-h-\[60vh\] {
  min-height: 60vh;
}
.min-h-\[80vh\] {
  min-height: 80vh;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-1 {
  width: 0.25rem;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-24 {
  width: 6rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-8 {
  width: 2rem;
}
.w-9 {
  width: 2.25rem;
}
.w-full {
  width: 100%;
}
.min-w-\[200px\] {
  min-width: 200px;
}
.min-w-\[220px\] {
  min-width: 220px;
}
.min-w-full {
  min-width: 100%;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.border-collapse {
  border-collapse: collapse;
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-2 {
  --tw-translate-y: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes fadeIn {

  0% {
    opacity: 0;
    transform: translateY(15px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fade-in {
  animation: fadeIn 1.2s ease-out forwards;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-16 {
  gap: 4rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-24 {
  gap: 6rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-y-3 {
  row-gap: 0.75rem;
}
.gap-y-4 {
  row-gap: 1rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}
.space-y-16 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-24 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(6rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(6rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-temple\/10 > :not([hidden]) ~ :not([hidden]) {
  border-color: rgb(139 115 85 / 0.1);
}
.self-start {
  align-self: flex-start;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-t-2xl {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r-4 {
  border-right-width: 4px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-4 {
  border-top-width: 4px;
}
.border-bamboo\/10 {
  border-color: rgb(156 175 136 / 0.1);
}
.border-bamboo\/20 {
  border-color: rgb(156 175 136 / 0.2);
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-golden\/20 {
  border-color: rgb(212 165 116 / 0.2);
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-rice\/20 {
  border-color: rgb(248 246 243 / 0.2);
}
.border-temple {
  --tw-border-opacity: 1;
  border-color: rgb(139 115 85 / var(--tw-border-opacity, 1));
}
.border-temple\/10 {
  border-color: rgb(139 115 85 / 0.1);
}
.border-temple\/20 {
  border-color: rgb(139 115 85 / 0.2);
}
.border-temple\/5 {
  border-color: rgb(139 115 85 / 0.05);
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}
.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}
.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-t-gray-900 {
  --tw-border-opacity: 1;
  border-top-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}
.bg-black\/5 {
  background-color: rgb(0 0 0 / 0.05);
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/5 {
  background-color: rgb(59 130 246 / 0.05);
}
.bg-current {
  background-color: currentColor;
}
.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.bg-emerald-500\/5 {
  background-color: rgb(16 185 129 / 0.05);
}
.bg-golden {
  --tw-bg-opacity: 1;
  background-color: rgb(212 165 116 / var(--tw-bg-opacity, 1));
}
.bg-golden\/10 {
  background-color: rgb(212 165 116 / 0.1);
}
.bg-golden\/5 {
  background-color: rgb(212 165 116 / 0.05);
}
.bg-golden\/60 {
  background-color: rgb(212 165 116 / 0.6);
}
.bg-golden\/90 {
  background-color: rgb(212 165 116 / 0.9);
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-lotus {
  --tw-bg-opacity: 1;
  background-color: rgb(232 213 196 / var(--tw-bg-opacity, 1));
}
.bg-lotus\/10 {
  background-color: rgb(232 213 196 / 0.1);
}
.bg-lotus\/5 {
  background-color: rgb(232 213 196 / 0.05);
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(26 26 26 / var(--tw-bg-opacity, 1));
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-rice {
  --tw-bg-opacity: 1;
  background-color: rgb(248 246 243 / var(--tw-bg-opacity, 1));
}
.bg-rice\/50 {
  background-color: rgb(248 246 243 / 0.5);
}
.bg-rice\/95 {
  background-color: rgb(248 246 243 / 0.95);
}
.bg-sage {
  --tw-bg-opacity: 1;
  background-color: rgb(168 181 160 / var(--tw-bg-opacity, 1));
}
.bg-sage\/10 {
  background-color: rgb(168 181 160 / 0.1);
}
.bg-sage\/5 {
  background-color: rgb(168 181 160 / 0.05);
}
.bg-sage\/60 {
  background-color: rgb(168 181 160 / 0.6);
}
.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(248 246 243 / var(--tw-bg-opacity, 1));
}
.bg-secondary\/95 {
  background-color: rgb(248 246 243 / 0.95);
}
.bg-shell\/20 {
  background-color: rgb(245 242 237 / 0.2);
}
.bg-shell\/30 {
  background-color: rgb(245 242 237 / 0.3);
}
.bg-shell\/40 {
  background-color: rgb(245 242 237 / 0.4);
}
.bg-shell\/60 {
  background-color: rgb(245 242 237 / 0.6);
}
.bg-shell\/80 {
  background-color: rgb(245 242 237 / 0.8);
}
.bg-soft-sage\/10 {
  background-color: rgb(232 229 224 / 0.1);
}
.bg-soft-sage\/20 {
  background-color: rgb(232 229 224 / 0.2);
}
.bg-temple {
  --tw-bg-opacity: 1;
  background-color: rgb(139 115 85 / var(--tw-bg-opacity, 1));
}
.bg-temple\/10 {
  background-color: rgb(139 115 85 / 0.1);
}
.bg-temple\/20 {
  background-color: rgb(139 115 85 / 0.2);
}
.bg-temple\/40 {
  background-color: rgb(139 115 85 / 0.4);
}
.bg-temple\/5 {
  background-color: rgb(139 115 85 / 0.05);
}
.bg-temple\/85 {
  background-color: rgb(139 115 85 / 0.85);
}
.bg-temple\/90 {
  background-color: rgb(139 115 85 / 0.9);
}
.bg-transparent {
  background-color: transparent;
}
.bg-warm-gold {
  --tw-bg-opacity: 1;
  background-color: rgb(212 165 116 / var(--tw-bg-opacity, 1));
}
.bg-warm-gold\/10 {
  background-color: rgb(212 165 116 / 0.1);
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}
.bg-white\/40 {
  background-color: rgb(255 255 255 / 0.4);
}
.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}
.bg-white\/60 {
  background-color: rgb(255 255 255 / 0.6);
}
.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}
.bg-wood {
  --tw-bg-opacity: 1;
  background-color: rgb(107 91 71 / var(--tw-bg-opacity, 1));
}
.bg-wood\/10 {
  background-color: rgb(107 91 71 / 0.1);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.from-bamboo\/10 {
  --tw-gradient-from: rgb(156 175 136 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(156 175 136 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-rice {
  --tw-gradient-from: #F8F6F3 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 246 243 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-rice\/20 {
  --tw-gradient-from: rgb(248 246 243 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 246 243 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-rice\/90 {
  --tw-gradient-from: rgb(248 246 243 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 246 243 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-shell\/10 {
  --tw-gradient-from: rgb(245 242 237 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 242 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-shell\/20 {
  --tw-gradient-from: rgb(245 242 237 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 242 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-shell\/30 {
  --tw-gradient-from: rgb(245 242 237 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 242 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-shell\/40 {
  --tw-gradient-from: rgb(245 242 237 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 242 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-shell\/60 {
  --tw-gradient-from: rgb(245 242 237 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 242 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-shell\/95 {
  --tw-gradient-from: rgb(245 242 237 / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 242 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-temple {
  --tw-gradient-from: #8B7355 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-temple\/10 {
  --tw-gradient-from: rgb(139 115 85 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-temple\/20 {
  --tw-gradient-from: rgb(139 115 85 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-temple\/5 {
  --tw-gradient-from: rgb(139 115 85 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-temple\/60 {
  --tw-gradient-from: rgb(139 115 85 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-temple\/70 {
  --tw-gradient-from: rgb(139 115 85 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 115 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-current {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), currentColor var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-golden\/5 {
  --tw-gradient-to: rgb(212 165 116 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(212 165 116 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-rice\/60 {
  --tw-gradient-to: rgb(248 246 243 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(248 246 243 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-rice\/80 {
  --tw-gradient-to: rgb(248 246 243 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(248 246 243 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-temple\/20 {
  --tw-gradient-to: rgb(139 115 85 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(139 115 85 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-warm-gold {
  --tw-gradient-to: rgb(212 165 116 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #D4A574 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-bamboo\/10 {
  --tw-gradient-to: rgb(156 175 136 / 0.1) var(--tw-gradient-to-position);
}
.to-bamboo\/20 {
  --tw-gradient-to: rgb(156 175 136 / 0.2) var(--tw-gradient-to-position);
}
.to-bamboo\/30 {
  --tw-gradient-to: rgb(156 175 136 / 0.3) var(--tw-gradient-to-position);
}
.to-bamboo\/5 {
  --tw-gradient-to: rgb(156 175 136 / 0.05) var(--tw-gradient-to-position);
}
.to-golden {
  --tw-gradient-to: #D4A574 var(--tw-gradient-to-position);
}
.to-golden\/5 {
  --tw-gradient-to: rgb(212 165 116 / 0.05) var(--tw-gradient-to-position);
}
.to-lotus\/20 {
  --tw-gradient-to: rgb(232 213 196 / 0.2) var(--tw-gradient-to-position);
}
.to-ocean\/10 {
  --tw-gradient-to: rgb(122 156 198 / 0.1) var(--tw-gradient-to-position);
}
.to-ocean\/30 {
  --tw-gradient-to: rgb(122 156 198 / 0.3) var(--tw-gradient-to-position);
}
.to-rice\/90 {
  --tw-gradient-to: rgb(248 246 243 / 0.9) var(--tw-gradient-to-position);
}
.to-temple\/5 {
  --tw-gradient-to: rgb(139 115 85 / 0.05) var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.fill-current {
  fill: currentColor;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.p-12 {
  padding: 3rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-24 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-32 {
  padding-top: 8rem;
  padding-bottom: 8rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-12 {
  padding-bottom: 3rem;
}
.pb-20 {
  padding-bottom: 5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-5 {
  padding-bottom: 1.25rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-48 {
  padding-top: 12rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-8 {
  padding-top: 2rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.font-display {
  font-family: var(--font-cormorant), Georgia, serif;
}
.font-serif {
  font-family: var(--font-cormorant), Georgia, serif;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2.3rem;
  letter-spacing: -0.01em;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.6rem;
  letter-spacing: -0.01em;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.8rem;
  letter-spacing: -0.02em;
}
.text-5xl {
  font-size: 3rem;
  line-height: 3.6rem;
  letter-spacing: -0.02em;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-base {
  font-size: 1rem;
  line-height: 1.8rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 2rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.6rem;
  letter-spacing: 0.1em;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 2.1rem;
  letter-spacing: -0.01em;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.italic {
  font-style: italic;
}
.not-italic {
  font-style: normal;
}
.leading-\[0\.95\] {
  line-height: 0.95;
}
.leading-loose {
  line-height: 2;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-\[0\.2em\] {
  letter-spacing: 0.2em;
}
.tracking-\[0\.3em\] {
  letter-spacing: 0.3em;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.text-accent {
  --tw-text-opacity: 1;
  color: rgb(139 115 85 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-current {
  color: currentColor;
}
.text-golden {
  --tw-text-opacity: 1;
  color: rgb(212 165 116 / var(--tw-text-opacity, 1));
}
.text-golden\/80 {
  color: rgb(212 165 116 / 0.8);
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-primary {
  --tw-text-opacity: 1;
  color: rgb(26 26 26 / var(--tw-text-opacity, 1));
}
.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-rice {
  --tw-text-opacity: 1;
  color: rgb(248 246 243 / var(--tw-text-opacity, 1));
}
.text-sage {
  --tw-text-opacity: 1;
  color: rgb(168 181 160 / var(--tw-text-opacity, 1));
}
.text-shell {
  --tw-text-opacity: 1;
  color: rgb(245 242 237 / var(--tw-text-opacity, 1));
}
.text-shell\/80 {
  color: rgb(245 242 237 / 0.8);
}
.text-temple {
  --tw-text-opacity: 1;
  color: rgb(139 115 85 / var(--tw-text-opacity, 1));
}
.text-temple\/30 {
  color: rgb(139 115 85 / 0.3);
}
.text-temple\/40 {
  color: rgb(139 115 85 / 0.4);
}
.text-temple\/50 {
  color: rgb(139 115 85 / 0.5);
}
.text-temple\/60 {
  color: rgb(139 115 85 / 0.6);
}
.text-temple\/70 {
  color: rgb(139 115 85 / 0.7);
}
.text-temple\/80 {
  color: rgb(139 115 85 / 0.8);
}
.text-temple\/85 {
  color: rgb(139 115 85 / 0.85);
}
.text-temple\/90 {
  color: rgb(139 115 85 / 0.9);
}
.text-warm-gold {
  --tw-text-opacity: 1;
  color: rgb(212 165 116 / var(--tw-text-opacity, 1));
}
.text-warm-gold\/5 {
  color: rgb(212 165 116 / 0.05);
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/60 {
  color: rgb(255 255 255 / 0.6);
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.text-wood-light {
  --tw-text-opacity: 1;
  color: rgb(139 115 85 / var(--tw-text-opacity, 1));
}
.text-wood-light\/50 {
  color: rgb(139 115 85 / 0.5);
}
.text-wood-light\/60 {
  color: rgb(139 115 85 / 0.6);
}
.text-wood-light\/70 {
  color: rgb(139 115 85 / 0.7);
}
.text-wood-light\/75 {
  color: rgb(139 115 85 / 0.75);
}
.text-wood-light\/80 {
  color: rgb(139 115 85 / 0.8);
}
.text-wood-light\/85 {
  color: rgb(139 115 85 / 0.85);
}
.text-wood-light\/90 {
  color: rgb(139 115 85 / 0.9);
}
.text-wood\/40 {
  color: rgb(107 91 71 / 0.4);
}
.text-wood\/80 {
  color: rgb(107 91 71 / 0.8);
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.placeholder-white\/60::-moz-placeholder {
  color: rgb(255 255 255 / 0.6);
}
.placeholder-white\/60::placeholder {
  color: rgb(255 255 255 / 0.6);
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-90 {
  opacity: 0.9;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-medium {
  --tw-shadow: var(--shadow-medium);
  --tw-shadow-colored: var(--shadow-medium);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-soft {
  --tw-shadow: var(--shadow-soft);
  --tw-shadow-colored: var(--shadow-soft);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-warm {
  --tw-shadow: var(--shadow-warm);
  --tw-shadow-colored: var(--shadow-warm);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline {
  outline-style: solid;
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-md {
  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.delay-300 {
  transition-delay: 300ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-700 {
  transition-duration: 700ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.section-padding {
  padding-top: 6rem;
  padding-bottom: 6rem;
}
@media (min-width: 1024px) {

  .section-padding {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}
.container-padding {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
@media (min-width: 640px) {

  .container-padding {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
@media (min-width: 1024px) {

  .container-padding {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}
.max-width-content {
  margin-left: auto;
  margin-right: auto;
  max-width: 80rem;
}
.shadow-soft {
    box-shadow: var(--shadow-soft);
  }
.shadow-medium {
    box-shadow: var(--shadow-medium);
  }
/* Profesjonalna galeria - jednolita siatka */
@keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(15px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
@keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
@keyframes floatGentle {
    0%, 100% { 
      transform: translateY(0px); 
    }
    50% { 
      transform: translateY(-4px); 
    }
  }
@keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
@keyframes hoverLift {
    to {
      transform: translateY(-2px);
    }
  }
@keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
.animate-fade-in {
    animation: fadeIn 1.2s ease forwards;
  }
.delay-300 { animation-delay: 300ms; }
/* Tło sekcji - jednolite kolory */
.section-bg {
    background: rgb(var(--color-secondary));
  }
/* Delikatne animacje */
.animate-gentle {
    animation: fadeInGentle 1.2s ease forwards;
    opacity: 0;
  }
@keyframes fadeInGentle {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
/* Opóźnienia dla animacji */
/* Dodatkowe klasy dla lepszej spójności */
/* Elegancki loader - mindful breathing */
.mindful-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }
.breath-circle {
    width: 60px;
    height: 60px;
    border: 2px solid rgb(var(--color-accent));
    border-radius: 50%;
    animation: breathe 4s ease-in-out infinite;
  }
.breath-text {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: rgb(var(--color-accent));
    font-weight: 300;
    letter-spacing: 0.1em;
  }
@keyframes breathe {
    0%, 100% {
      transform: scale(1);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.2);
      opacity: 1;
    }
  }
/* Fallback loader */
.loader {
    width: 40px;
    height: 40px;
    border: 1px solid rgb(var(--color-soft-sage));
    border-top-color: rgb(var(--color-accent));
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
@keyframes spin {
    to { transform: rotate(360deg); }
  }
/* Mikro-animacje dla liczb/statystyk */
.stat-number {
    font-variant-numeric: tabular-nums;
    letter-spacing: -0.02em;
    font-weight: 300;
  }
@keyframes countUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
.animate-count-up {
    animation: countUp 0.8s ease forwards;
  }
/* Eleganckie powiadomienia/toasty */
.toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: rgb(var(--color-primary));
    color: rgb(var(--color-secondary));
    padding: 1rem 2rem;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    font-weight: 300;
    border-radius: 0;
    box-shadow: var(--shadow-medium);
    animation: slideInToast 0.3s ease;
    z-index: 1000;
  }
@keyframes slideInToast {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
.toast.success {
    background: rgb(var(--color-accent));
    color: rgb(var(--color-secondary));
  }
.toast.error {
    background: #dc2626;
    color: white;
  }
/* Opcjonalne wsparcie dla dark mode */
@media (prefers-color-scheme: dark) {
    :root {
      --color-primary: 248 246 243;
      --color-secondary: 26 26 26;
      --color-accent: 139 115 85;
      --color-soft-sage: 60 60 60;
      --color-warm-gold: 212 165 116;
    }
  }
/* Smooth transitions dla zmiany motywu */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }
/* Spójne style dla wszystkich kontenerów */
/* Spójne style dla wszystkich grid-ów */

/* ==========================================================================
   CSS Variables - Harmonijna paleta kolorów inspirowana Bali i jogą (wersja ultra-delikatna)
   ========================================================================== */

@media (max-width: 768px) {
  .hero-section {
    min-height: 85vh;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Więcej przestrzeni na mobile - nie zmniejszaj paddingów */
  .section-padding {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .card-content {
    padding: 2rem;
  }

  .btn {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.6rem;
    letter-spacing: 0.1em;
  }

  .nav-link {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .hero-title {
    font-size: 1.875rem;
    line-height: 2.6rem;
    letter-spacing: -0.01em;
  }

  @media (min-width: 768px) {

    .hero-title {
      font-size: 2.25rem;
      line-height: 2.8rem;
      letter-spacing: -0.02em;
    }
  }

  .hero-subtitle {
    font-size: 1.125rem;
    line-height: 2rem;
  }

  @media (min-width: 768px) {

    .hero-subtitle {
      font-size: 1.25rem;
      line-height: 2.1rem;
      letter-spacing: -0.01em;
    }
  }

  .animate-fade-in,
  .animate-slide-up,
  .animate-scale-in {
    animation-duration: 0.8s;
  }

  /* Ukryj mniej ważne elementy na mobile */
  .mobile-hidden {
    display: none;
  }

  /* Kreatywny stack - niektóre elementy obok siebie */
  .mobile-creative-stack {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  /* Section numbers mniejsze na mobile */
  .section-number {
    font-size: clamp(3rem, 8vw, 6rem);
    top: 1rem;
    left: 1rem;
  }

  /* Quote huge mniejszy na mobile */
  .quote-huge {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
  }

  .quote-huge::before {
    font-size: clamp(2rem, 6vw, 4rem);
    top: -1rem;
    left: -1rem;
  }

  /* Gesture hints */
  .gesture-hint {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem;
    border-radius: 50%;
    animation: bounce 2s infinite;
    z-index: 40;
  }

  /* Swipe hint dla mobile */
  .swipe-hint {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: swipeUp 2s ease-in-out infinite;
    color: rgb(var(--color-accent) / 0.7);
    font-size: 0.875rem;
    font-weight: 300;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  @keyframes swipeUp {
    0%, 100% {
      transform: translateX(-50%) translateY(0);
      opacity: 0.7;
    }
    50% {
      transform: translateX(-50%) translateY(-10px);
      opacity: 1;
    }
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 80vh;
  }

  .section-padding {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .card-content {
    padding: 1rem;
  }

  .btn {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-fade-in,
  .animate-slide-up,
  .animate-scale-in,
  .animate-hover-lift,
  .animate-float {
    animation: none !important;
  }
}

@media print {
  * {
    box-shadow: none !important;
    text-shadow: none !important;
    background: transparent !important;
    color: black !important;
  }
  
  .navbar,
  .btn,
  .hero-section,
  footer {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }

  a,
  a:visited {
    text-decoration: underline;
    color: #444 !important;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  img {
    max-width: 100% !important;
    page-break-inside: avoid;
  }

  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }

  h2, h3 {
    page-break-after: avoid;
  }

  .card {
    border: 1px solid #ddd !important;
    page-break-inside: avoid;
  }
}

/* React Big Calendar Styles - loaded dynamically in component */

.custom-calendar {
  font-family: var(--font-inter);
}

.custom-calendar .rbc-header {
  background-color: rgb(var(--color-primary) / 0.05);
  color: rgb(var(--color-primary));
  font-weight: 500;
  padding: 12px 8px;
  border-bottom: 1px solid rgb(var(--color-primary) / 0.1);
}

.custom-calendar .rbc-today {
  background-color: rgb(var(--color-primary) / 0.05);
}

.custom-calendar .rbc-off-range-bg {
  background-color: rgb(var(--color-primary) / 0.02);
}

.custom-calendar .rbc-event {
  border-radius: 6px;
  border: none;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
}

.custom-calendar .rbc-event:focus {
  outline: 2px solid rgb(var(--color-primary) / 0.5);
}

.custom-calendar .rbc-toolbar {
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 12px;
}

.custom-calendar .rbc-toolbar button {
  background-color: white;
  border: 1px solid rgb(var(--color-primary) / 0.2);
  color: rgb(var(--color-primary));
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.custom-calendar .rbc-toolbar button:hover {
  background-color: rgb(var(--color-primary) / 0.05);
  border-color: rgb(var(--color-primary) / 0.3);
}

.custom-calendar .rbc-toolbar button.rbc-active {
  background-color: rgb(var(--color-primary));
  color: white;
  border-color: rgb(var(--color-primary));
}

.custom-calendar .rbc-month-view {
  border: 1px solid rgb(var(--color-primary) / 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.custom-calendar .rbc-date-cell {
  padding: 8px;
  text-align: center;
}

.custom-calendar .rbc-date-cell > a {
  color: rgb(var(--color-primary));
  font-weight: 500;
}

@media (max-width: 768px) {
  .custom-calendar .rbc-toolbar {
    flex-direction: column;
    align-items: center;
  }

  .custom-calendar .rbc-toolbar-label {
    order: -1;
    margin-bottom: 12px;
    font-size: 18px;
    font-weight: 600;
  }
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.6rem;
  letter-spacing: 0.1em;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-3:hover {
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:animate-none:hover {
  animation: none;
}

.hover\:gap-3:hover {
  gap: 0.75rem;
}

.hover\:border-current:hover {
  border-color: currentColor;
}

.hover\:border-temple\/20:hover {
  border-color: rgb(139 115 85 / 0.2);
}

.hover\:border-temple\/40:hover {
  border-color: rgb(139 115 85 / 0.4);
}

.hover\:border-warm-gold:hover {
  --tw-border-opacity: 1;
  border-color: rgb(212 165 116 / var(--tw-border-opacity, 1));
}

.hover\:bg-golden:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(212 165 116 / var(--tw-bg-opacity, 1));
}

.hover\:bg-golden\/90:hover {
  background-color: rgb(212 165 116 / 0.9);
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lotus\/90:hover {
  background-color: rgb(232 213 196 / 0.9);
}

.hover\:bg-primary\/80:hover {
  background-color: rgb(26 26 26 / 0.8);
}

.hover\:bg-rice\/80:hover {
  background-color: rgb(248 246 243 / 0.8);
}

.hover\:bg-sage\/90:hover {
  background-color: rgb(168 181 160 / 0.9);
}

.hover\:bg-secondary\/80:hover {
  background-color: rgb(248 246 243 / 0.8);
}

.hover\:bg-shell\/10:hover {
  background-color: rgb(245 242 237 / 0.1);
}

.hover\:bg-shell\/30:hover {
  background-color: rgb(245 242 237 / 0.3);
}

.hover\:bg-temple:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(139 115 85 / var(--tw-bg-opacity, 1));
}

.hover\:bg-temple\/10:hover {
  background-color: rgb(139 115 85 / 0.1);
}

.hover\:bg-temple\/20:hover {
  background-color: rgb(139 115 85 / 0.2);
}

.hover\:bg-temple\/5:hover {
  background-color: rgb(139 115 85 / 0.05);
}

.hover\:bg-temple\/90:hover {
  background-color: rgb(139 115 85 / 0.9);
}

.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}

.hover\:bg-white\/90:hover {
  background-color: rgb(255 255 255 / 0.9);
}

.hover\:bg-wood\/90:hover {
  background-color: rgb(107 91 71 / 0.9);
}

.hover\:text-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.hover\:text-golden:hover {
  --tw-text-opacity: 1;
  color: rgb(212 165 116 / var(--tw-text-opacity, 1));
}

.hover\:text-green-900:hover {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

.hover\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.hover\:text-temple:hover {
  --tw-text-opacity: 1;
  color: rgb(139 115 85 / var(--tw-text-opacity, 1));
}

.hover\:text-temple\/70:hover {
  color: rgb(139 115 85 / 0.7);
}

.hover\:text-temple\/80:hover {
  color: rgb(139 115 85 / 0.8);
}

.hover\:text-warm-gold:hover {
  --tw-text-opacity: 1;
  color: rgb(212 165 116 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:no-underline:hover {
  text-decoration-line: none;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-70:hover {
  opacity: 0.7;
}

.hover\:shadow-glow:hover {
  --tw-shadow: var(--shadow-glow);
  --tw-shadow-colored: var(--shadow-glow);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-medium:hover {
  --tw-shadow: var(--shadow-medium);
  --tw-shadow-colored: var(--shadow-medium);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-medium:hover {
    box-shadow: var(--shadow-medium);
  }

.focus\:border-temple:focus {
  --tw-border-opacity: 1;
  border-color: rgb(139 115 85 / var(--tw-border-opacity, 1));
}

.focus\:border-temple\/50:focus {
  border-color: rgb(139 115 85 / 0.5);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-inset:focus {
  --tw-ring-inset: inset;
}

.focus\:ring-golden\/30:focus {
  --tw-ring-color: rgb(212 165 116 / 0.3);
}

.focus\:ring-temple\/20:focus {
  --tw-ring-color: rgb(139 115 85 / 0.2);
}

.focus\:ring-temple\/30:focus {
  --tw-ring-color: rgb(139 115 85 / 0.3);
}

.focus\:ring-temple\/50:focus {
  --tw-ring-color: rgb(139 115 85 / 0.5);
}

.focus\:ring-white\/20:focus {
  --tw-ring-color: rgb(255 255 255 / 0.2);
}

.focus\:ring-white\/50:focus {
  --tw-ring-color: rgb(255 255 255 / 0.5);
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-offset-1:focus-visible {
  --tw-ring-offset-width: 1px;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group:hover .group-hover\:visible {
  visibility: visible;
}

.group:hover .group-hover\:left-0 {
  left: 0px;
}

.group\/item:hover .group-hover\/item\:w-2 {
  width: 0.5rem;
}

.group:hover .group-hover\:w-full {
  width: 100%;
}

.group:hover .group-hover\:-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-0\.5 {
  --tw-translate-x: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-golden\/10 {
  background-color: rgb(212 165 116 / 0.1);
}

.group:hover .group-hover\:bg-temple\/20 {
  background-color: rgb(139 115 85 / 0.2);
}

.group:hover .group-hover\:bg-warm-gold\/20 {
  background-color: rgb(212 165 116 / 0.2);
}

.group:hover .group-hover\:text-golden {
  --tw-text-opacity: 1;
  color: rgb(212 165 116 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-temple {
  --tw-text-opacity: 1;
  color: rgb(139 115 85 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-warm-gold {
  --tw-text-opacity: 1;
  color: rgb(212 165 116 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}

@media (min-width: 640px) {

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 3.6rem;
    letter-spacing: -0.02em;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 2.1rem;
    letter-spacing: -0.01em;
  }
}

@media (min-width: 768px) {

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-12 {
    margin-bottom: 3rem;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-20 {
    height: 5rem;
  }

  .md\:h-80 {
    height: 20rem;
  }

  .md\:h-96 {
    height: 24rem;
  }

  .md\:h-\[340px\] {
    height: 340px;
  }

  .md\:min-h-\[90vh\] {
    min-height: 90vh;
  }

  .md\:w-2\/5 {
    width: 40%;
  }

  .md\:w-3\/5 {
    width: 60%;
  }

  .md\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:p-12 {
    padding: 3rem;
  }

  .md\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .md\:py-28 {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .md\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.6rem;
    letter-spacing: -0.01em;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.8rem;
    letter-spacing: -0.02em;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 3.6rem;
    letter-spacing: -0.02em;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.6rem;
    letter-spacing: 0.1em;
  }
}

@media (min-width: 1024px) {

  .lg\:-ml-16 {
    margin-left: -4rem;
  }

  .lg\:-mr-16 {
    margin-right: -4rem;
  }

  .lg\:ml-8 {
    margin-left: 2rem;
  }

  .lg\:mr-8 {
    margin-right: 2rem;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:gap-16 {
    gap: 4rem;
  }

  .lg\:gap-32 {
    gap: 8rem;
  }

  .lg\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.8rem;
    letter-spacing: -0.02em;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 3.6rem;
    letter-spacing: -0.02em;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }
}

@media (min-width: 1280px) {

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}

.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.jsx","import":"Montserrat","arguments":[{"subsets":["latin"],"variable":"--font-montserrat","weight":["300","400","500"],"display":"swap"}],"variableName":"montserrat"} ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/3f69592b2fe603c7-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/6325a8417175c41d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/021bc4481ed92ece-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/99b7f73d5af7c3e2-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/4f05ba3a6752a328-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/3f69592b2fe603c7-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/6325a8417175c41d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/021bc4481ed92ece-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/99b7f73d5af7c3e2-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/4f05ba3a6752a328-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/3f69592b2fe603c7-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/6325a8417175c41d-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/021bc4481ed92ece-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/99b7f73d5af7c3e2-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/4f05ba3a6752a328-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Montserrat Fallback';src: local("Arial");ascent-override: 85.79%;descent-override: 22.25%;line-gap-override: 0.00%;size-adjust: 112.83%
}.__className_515534 {font-family: 'Montserrat', 'Montserrat Fallback';font-style: normal
}.__variable_515534 {--font-montserrat: 'Montserrat', 'Montserrat Fallback'
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.jsx","import":"Cormorant_Garamond","arguments":[{"subsets":["latin"],"variable":"--font-cormorant","weight":["300","400"],"display":"swap"}],"variableName":"cormorant"} ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/a2bfe7f39b1eebf5-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/5676475b14971f9e-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/eb6885ee7e3f5299-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/0d293583de0bf52f-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/fc6b86356f45d8cd-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/a2bfe7f39b1eebf5-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/5676475b14971f9e-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/eb6885ee7e3f5299-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/0d293583de0bf52f-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Cormorant Garamond';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/fc6b86356f45d8cd-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Cormorant Garamond Fallback';src: local("Times New Roman");ascent-override: 95.27%;descent-override: 29.59%;line-gap-override: 0.00%;size-adjust: 96.98%
}.__className_37cd3e {font-family: 'Cormorant Garamond', 'Cormorant Garamond Fallback';font-style: normal
}.__variable_37cd3e {--font-cormorant: 'Cormorant Garamond', 'Cormorant Garamond Fallback'
}

